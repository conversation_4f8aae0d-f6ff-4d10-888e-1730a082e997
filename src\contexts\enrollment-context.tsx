'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { architectureCourse } from '@/constants/shared-course-data';
import { Course } from '@/types/lms';

interface EnrollmentData {
  isEnrolled: boolean;
  courseData: Course;
  enrollmentTimestamp: number;
  expirationTime: number; // 10 minutes in milliseconds
}

interface EnrollmentContextType {
  isEnrolled: boolean;
  courseData: Course;
  enrollInCourse: () => void;
  enrollInCourseWithPurchase: (course: Course) => void;
  updateCourseProgress: (updatedCourse: Course) => void;
  enrolledCourses: Course[];
}

const EnrollmentContext = createContext<EnrollmentContextType | undefined>(
  undefined
);

export const useEnrollment = () => {
  const context = useContext(EnrollmentContext);
  if (!context) {
    throw new Error('useEnrollment must be used within an EnrollmentProvider');
  }
  return context;
};

interface EnrollmentProviderProps {
  children: ReactNode;
}

export const EnrollmentProvider: React.FC<EnrollmentProviderProps> = ({
  children
}) => {
  const [isEnrolled, setIsEnrolled] = useState(false);
  const [courseData, setCourseData] = useState<Course>(architectureCourse);
  const [enrolledCourses, setEnrolledCourses] = useState<Course[]>([]);

  const STORAGE_KEY = 'lms-enrollment-data';
  const EXPIRATION_TIME = 10 * 60 * 1000; // 10 minutes in milliseconds

  // Load persisted data on component mount
  useEffect(() => {
    const loadPersistedData = () => {
      try {
        const stored = localStorage.getItem(STORAGE_KEY);
        if (stored) {
          const enrollmentData: EnrollmentData = JSON.parse(stored);
          const now = Date.now();
          
          // Check if enrollment has expired
          if (now < enrollmentData.expirationTime) {
            setIsEnrolled(enrollmentData.isEnrolled);
            setCourseData(enrollmentData.courseData);
            setEnrolledCourses([enrollmentData.courseData]);
          } else {
            // Clear expired data
            localStorage.removeItem(STORAGE_KEY);
          }
        }
      } catch (error) {
        console.error('Failed to load enrollment data:', error);
        localStorage.removeItem(STORAGE_KEY);
      }
    };

    loadPersistedData();
  }, []);

  // Persist enrollment data to localStorage
  const persistEnrollmentData = (enrolled: boolean, course: Course) => {
    const now = Date.now();
    const enrollmentData: EnrollmentData = {
      isEnrolled: enrolled,
      courseData: course,
      enrollmentTimestamp: now,
      expirationTime: now + EXPIRATION_TIME
    };

    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(enrollmentData));
      setEnrolledCourses([course]);
      
      // Set up automatic cleanup after expiration
      setTimeout(() => {
        localStorage.removeItem(STORAGE_KEY);
        setIsEnrolled(false);
        setEnrolledCourses([]);
        setCourseData(architectureCourse);
      }, EXPIRATION_TIME);
    } catch (error) {
      console.error('Failed to persist enrollment data:', error);
    }
  };

  const enrollInCourse = () => {
    setIsEnrolled(true);
    const updatedCourse = {
      ...architectureCourse,
      status: 'in-progress' as const
    };
    setCourseData(updatedCourse);
    persistEnrollmentData(true, updatedCourse);
  };

  const enrollInCourseWithPurchase = (course: Course) => {
    setIsEnrolled(true);
    const updatedCourse = {
      ...course,
      status: 'in-progress' as const,
      totalProgress: 0
    };
    setCourseData(updatedCourse);
    persistEnrollmentData(true, updatedCourse);
  };

  const updateCourseProgress = (updatedCourse: Course) => {
    setCourseData(updatedCourse);
    // Update persisted data with new progress
    if (isEnrolled) {
      persistEnrollmentData(true, updatedCourse);
    }
  };

  const value = {
    isEnrolled,
    courseData,
    enrollInCourse,
    enrollInCourseWithPurchase,
    updateCourseProgress,
    enrolledCourses
  };

  return (
    <EnrollmentContext.Provider value={value}>
      {children}
    </EnrollmentContext.Provider>
  );
};