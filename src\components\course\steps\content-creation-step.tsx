'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea'; // Keep for other uses
import { DynamicContentEditor, ContentBlock } from '@/components/dynamic-content-editor';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { 
  FileText, 
  HelpCircle, 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  Save,
  BookOpen,
  CheckCircle,
  Clock
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { CourseData, ModuleData, ChapterData, QuizData, QuestionData } from '../course-creation-wizard';
import { toast } from 'sonner';

interface ContentCreationStepProps {
  data: CourseData;
  onUpdate: (updates: Partial<CourseData>) => void;
}

export function ContentCreationStep({ data, onUpdate }: ContentCreationStepProps) {
  const [selectedModule, setSelectedModule] = useState<string>(data.modules[0]?.id || '');
  const [selectedChapter, setSelectedChapter] = useState<string>('');
  const [editingQuiz, setEditingQuiz] = useState<{ type: 'chapter' | 'module' | 'final'; quiz: QuizData | null }>({
    type: 'chapter',
    quiz: null
  });
  const [isQuizDialogOpen, setIsQuizDialogOpen] = useState(false);
  const [editingQuestion, setEditingQuestion] = useState<QuestionData | null>(null);
  const [isQuestionDialogOpen, setIsQuestionDialogOpen] = useState(false);
  const [previewMode, setPreviewMode] = useState(false);

  const currentModule = data.modules.find(m => m.id === selectedModule);
  const currentChapter = currentModule?.chapters.find(c => c.id === selectedChapter);

  const updateChapterContent = (content: ContentBlock[]) => {
    if (!currentModule || !currentChapter) return;

    const updatedModules = data.modules.map(module => {
      if (module.id === selectedModule) {
        const updatedChapters = module.chapters.map(chapter => {
          if (chapter.id === selectedChapter) {
            return { ...chapter, content };
          }
          return chapter;
        });
        return { ...module, chapters: updatedChapters };
      }
      return module;
    });

    onUpdate({ modules: updatedModules });
  };

  const createQuiz = (type: 'chapter' | 'module' | 'final') => {
    const newQuiz: QuizData = {
      id: `quiz-${Date.now()}`,
      name: type === 'chapter' ? `Quiz ${currentChapter?.name}` : 
            type === 'module' ? `Quiz ${currentModule?.name}` : 
            `Final Exam - ${data.name}`,
      description: '',
      questions: [],
      minimumScore: 70
    };
    
    setEditingQuiz({ type, quiz: newQuiz });
    setIsQuizDialogOpen(true);
  };

  const editQuiz = (type: 'chapter' | 'module' | 'final', quiz: QuizData) => {
    setEditingQuiz({ type, quiz: { ...quiz } });
    setIsQuizDialogOpen(true);
  };

  const saveQuiz = () => {
    if (!editingQuiz.quiz || !editingQuiz.quiz.name.trim()) {
      toast.error('Nama quiz harus diisi');
      return;
    }

    if (editingQuiz.type === 'final') {
      onUpdate({ finalExam: editingQuiz.quiz! });
    } else {
      const updatedModules = data.modules.map(module => {
        if (module.id === selectedModule) {
          if (editingQuiz.type === 'module') {
            return { ...module, moduleQuiz: editingQuiz.quiz! };
          } else {
            const updatedChapters = module.chapters.map(chapter => {
              if (chapter.id === selectedChapter) {
                return { ...chapter, chapterQuiz: editingQuiz.quiz! };
              }
              return chapter;
            });
            return { ...module, chapters: updatedChapters };
          }
        }
        return module;
      });
      onUpdate({ modules: updatedModules });
    }

    setIsQuizDialogOpen(false);
    setEditingQuiz({ type: 'chapter', quiz: null });
    toast.success('Quiz berhasil disimpan');
  };

  const createQuestion = () => {
    const newQuestion: QuestionData = {
      id: editingQuestion?.id || `question-${Date.now()}`,
      type: 'multiple_choice',
      question: [{ type: 'text', value: '' }],
      options: editingQuestion?.type === 'true_false'
        ? [
            { content: [{ type: 'text', value: 'True' }], isCorrect: false },
            { content: [{ type: 'text', value: 'False' }], isCorrect: false }
          ]
        : [
            { content: [{ type: 'text', value: '' }], isCorrect: false },
            { content: [{ type: 'text', value: '' }], isCorrect: false },
            { content: [{ type: 'text', value: '' }], isCorrect: false },
            { content: [{ type: 'text', value: '' }], isCorrect: false }
          ],
      essayAnswer: '',
      explanation: [],
      points: 1,
      orderIndex: editingQuiz.quiz?.questions.length || 0
    };
    
    setEditingQuestion(newQuestion);
    setIsQuestionDialogOpen(true);
  };

  const editQuestion = (question: QuestionData) => {
    setEditingQuestion({ ...question });
    setIsQuestionDialogOpen(true);
  };

  const saveQuestion = () => {
    if (!editingQuestion || editingQuestion.question.length === 0 || (editingQuestion.question[0].type === 'text' && !editingQuestion.question[0].value.trim())) {
      toast.error('Pertanyaan harus diisi');
      return;
    }

    if (!editingQuiz.quiz) return;

    const updatedQuestions = [...editingQuiz.quiz.questions];
    const existingIndex = updatedQuestions.findIndex(q => q.id === editingQuestion.id);
    
    if (existingIndex >= 0) {
      updatedQuestions[existingIndex] = editingQuestion;
    } else {
      updatedQuestions.push(editingQuestion);
    }

    setEditingQuiz(prev => ({
      ...prev,
      quiz: prev.quiz ? { ...prev.quiz, questions: updatedQuestions } : null
    }));
    
    setIsQuestionDialogOpen(false);
    setEditingQuestion(null);
    toast.success('Pertanyaan berhasil disimpan');
  };

  const deleteQuestion = (questionId: string) => {
    if (!editingQuiz.quiz) return;

    const updatedQuestions = editingQuiz.quiz.questions
      .filter(q => q.id !== questionId)
      .map((q, index) => ({ ...q, orderIndex: index }));

    setEditingQuiz(prev => ({
      ...prev,
      quiz: prev.quiz ? { ...prev.quiz, questions: updatedQuestions } : null
    }));
    
    toast.success('Pertanyaan berhasil dihapus');
  };

  const getCompletionStatus = () => {
    const totalChapters = data.modules.reduce((acc, module) => acc + module.chapters.length, 0);
    const completedChapters = data.modules.reduce((acc, module) => 
      acc + module.chapters.filter(chapter => chapter.content && chapter.content.length > 0).length, 0
    );
    
    return {
      total: totalChapters,
      completed: completedChapters,
      percentage: totalChapters > 0 ? Math.round((completedChapters / totalChapters) * 100) : 0
    };
  };

  const completionStatus = getCompletionStatus();

  if (data.modules.length === 0) {
    return (
      <div className="text-center py-12">
        <BookOpen className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
        <h3 className="text-lg font-semibold mb-2">Belum ada modul</h3>
        <p className="text-muted-foreground">
          Kembali ke langkah sebelumnya untuk membuat struktur modul terlebih dahulu
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Progress */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Pembuatan Konten</h3>
          <p className="text-sm text-muted-foreground">
            Tambahkan konten dan quiz untuk setiap chapter
          </p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="text-right">
            <div className="text-sm font-medium">
              {completionStatus.completed} / {completionStatus.total} Chapter
            </div>
            <div className="text-xs text-muted-foreground">
              {completionStatus.percentage}% selesai
            </div>
          </div>
          <div className={cn(
            "w-12 h-12 rounded-full flex items-center justify-center",
            completionStatus.percentage === 100 
              ? "bg-green-100 text-green-600" 
              : "bg-muted text-muted-foreground"
          )}>
            {completionStatus.percentage === 100 ? (
              <CheckCircle className="w-6 h-6" />
            ) : (
              <Clock className="w-6 h-6" />
            )}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Module/Chapter Navigation */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Navigasi Konten</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Final Exam Section */}
              <div className="space-y-2">
                <div className="p-3 rounded-lg border-2 border-dashed border-primary/30 bg-primary/5">
                  <div className="flex items-center justify-between mb-2">
                    <div>
                      <div className="font-medium text-sm text-primary">Final Exam</div>
                      <div className="text-xs text-muted-foreground">
                        Ujian akhir untuk seluruh course
                      </div>
                    </div>
                    {data.finalExam && (
                      <CheckCircle className="w-4 h-4 text-green-600" />
                    )}
                  </div>
                  <Button
                    variant={data.finalExam ? "outline" : "default"}
                    size="sm"
                    className="w-full"
                    onClick={() => {
                      if (data.finalExam) {
                        editQuiz('final', data.finalExam);
                      } else {
                        createQuiz('final');
                      }
                    }}
                  >
                    <HelpCircle className="w-4 h-4 mr-2" />
                    {data.finalExam ? 'Edit Final Exam' : 'Buat Final Exam'}
                  </Button>
                </div>
              </div>

              {/* Modules */}
              {data.modules.map(module => (
                <div key={module.id} className="space-y-2">
                  <div 
                    className={cn(
                      "p-2 rounded-lg cursor-pointer transition-colors",
                      selectedModule === module.id 
                        ? "bg-primary text-primary-foreground" 
                        : "bg-muted hover:bg-muted/80"
                    )}
                    onClick={() => {
                      setSelectedModule(module.id);
                      setSelectedChapter('');
                    }}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-medium text-sm">{module.name}</div>
                        <div className="text-xs opacity-75">
                          {module.chapters.length} chapters
                        </div>
                      </div>
                      {module.moduleQuiz && (
                        <Badge variant="secondary" className="text-xs">
                          <HelpCircle className="w-3 h-3 mr-1" />
                          Quiz
                        </Badge>
                      )}
                    </div>
                  </div>
                  
                  {selectedModule === module.id && (
                    <div className="ml-4 space-y-2">
                      {/* Module Quiz Button */}
                      <div className="p-2 rounded bg-secondary/50">
                        <Button
                          variant={module.moduleQuiz ? "outline" : "secondary"}
                          size="sm"
                          className="w-full text-xs"
                          onClick={() => {
                            if (module.moduleQuiz) {
                              editQuiz('module', module.moduleQuiz);
                            } else {
                              createQuiz('module');
                            }
                          }}
                        >
                          <HelpCircle className="w-3 h-3 mr-1" />
                          {module.moduleQuiz ? 'Edit Module Quiz' : 'Buat Module Quiz'}
                        </Button>
                      </div>
                      
                      {/* Chapters */}
                      {module.chapters.map(chapter => {
                        const hasContent = chapter.content && chapter.content.length > 0;
                        return (
                          <div
                            key={chapter.id}
                            className={cn(
                              "p-2 rounded text-xs cursor-pointer transition-colors flex items-center justify-between",
                              selectedChapter === chapter.id
                                ? "bg-primary/20 text-primary"
                                : "hover:bg-muted/50"
                            )}
                            onClick={() => setSelectedChapter(chapter.id)}
                          >
                            <span>{chapter.name}</span>
                            {hasContent && (
                              <CheckCircle className="w-3 h-3 text-green-600" />
                            )}
                          </div>
                        );
                      })}
                    </div>
                  )}
                </div>
              ))}
            </CardContent>
          </Card>
        </div>

        {/* Content Editor */}
        <div className="lg:col-span-3">
          {!selectedChapter ? (
            <div className="space-y-6">
              {/* Final Exam Info */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <HelpCircle className="w-5 h-5 text-primary" />
                    <span>Final Exam</span>
                    {data.finalExam && (
                      <Badge variant="secondary">Sudah dibuat</Badge>
                    )}
                  </CardTitle>
                  <CardDescription>
                    Ujian akhir untuk menguji pemahaman siswa terhadap seluruh materi course
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {data.finalExam ? (
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="text-center p-4 bg-muted rounded-lg">
                          <div className="text-2xl font-bold text-primary">
                            {data.finalExam.questions.length}
                          </div>
                          <div className="text-sm text-muted-foreground">Pertanyaan</div>
                        </div>
                        <div className="text-center p-4 bg-muted rounded-lg">
                          <div className="text-2xl font-bold text-primary">
                            {data.finalExam.minimumScore}%
                          </div>
                          <div className="text-sm text-muted-foreground">Nilai Minimum</div>
                        </div>
                        <div className="text-center p-4 bg-muted rounded-lg">
                          <div className="text-2xl font-bold text-primary">
                            {data.finalExam.questions.reduce((sum, q) => sum + q.points, 0)}
                          </div>
                          <div className="text-sm text-muted-foreground">Total Poin</div>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <Button
                          onClick={() => editQuiz('final', data.finalExam!)}
                          className="flex-1"
                        >
                          <Edit className="w-4 h-4 mr-2" />
                          Edit Final Exam
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <HelpCircle className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-semibold mb-2">Belum ada Final Exam</h3>
                      <p className="text-muted-foreground mb-4">
                        Final Exam adalah ujian akhir yang menguji pemahaman siswa terhadap seluruh materi course
                      </p>
                      <Button onClick={() => createQuiz('final')}>
                        <Plus className="w-4 h-4 mr-2" />
                        Buat Final Exam
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Module Overview */}
              <Card>
                <CardHeader>
                  <CardTitle>Overview Modul</CardTitle>
                  <CardDescription>
                    Pilih chapter dari navigasi di sebelah kiri untuk mulai menambahkan konten
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {data.modules.map(module => (
                      <div key={module.id} className="p-4 border rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium">{module.name}</h4>
                          {module.moduleQuiz && (
                            <Badge variant="secondary">
                              <HelpCircle className="w-3 h-3 mr-1" />
                              Quiz
                            </Badge>
                          )}
                        </div>
                        <div className="text-sm text-muted-foreground mb-3">
                          {module.chapters.length} chapters
                        </div>
                        <div className="space-y-1">
                          {module.chapters.map(chapter => {
                            const hasContent = chapter.content && chapter.content.length > 0;
                            return (
                              <div key={chapter.id} className="flex items-center justify-between text-xs">
                                <span>{chapter.name}</span>
                                {hasContent ? (
                                  <CheckCircle className="w-3 h-3 text-green-600" />
                                ) : (
                                  <Clock className="w-3 h-3 text-muted-foreground" />
                                )}
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          ) : (
            <div className="space-y-6">
              {/* Chapter Header */}
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="flex items-center space-x-2">
                        <span>{currentChapter?.name}</span>
                        {currentChapter?.hasChapterQuiz && (
                          <Badge variant="secondary">
                            <HelpCircle className="w-3 h-3 mr-1" />
                            Chapter Quiz
                          </Badge>
                        )}
                        {currentModule?.moduleQuiz && (
                          <Badge variant="outline">
                            <HelpCircle className="w-3 h-3 mr-1" />
                            Module Quiz
                          </Badge>
                        )}
                      </CardTitle>
                      <CardDescription>
                        Modul: {currentModule?.name}
                        {currentModule?.moduleQuiz && (
                          <span className="ml-2 text-xs text-primary">
                            • Module ini memiliki quiz
                          </span>
                        )}
                      </CardDescription>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setPreviewMode(!previewMode)}
                      >
                        <Eye className="w-4 h-4 mr-2" />
                        {previewMode ? 'Edit' : 'Preview'}
                      </Button>
                      {currentModule?.moduleQuiz && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => editQuiz('module', currentModule.moduleQuiz!)}
                        >
                          <HelpCircle className="w-4 h-4 mr-2" />
                          Edit Module Quiz
                        </Button>
                      )}
                      {currentChapter?.hasChapterQuiz && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            if (currentChapter.chapterQuiz) {
                              editQuiz('chapter', currentChapter.chapterQuiz);
                            } else {
                              createQuiz('chapter');
                            }
                          }}
                        >
                          <HelpCircle className="w-4 h-4 mr-2" />
                          {currentChapter.chapterQuiz ? 'Edit Chapter Quiz' : 'Buat Chapter Quiz'}
                        </Button>
                      )}
                    </div>
                  </div>
                </CardHeader>
              </Card>

              {/* Content Editor/Preview */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Konten Chapter</CardTitle>
                  <CardDescription>
                    {previewMode 
                      ? 'Preview konten seperti yang akan dilihat siswa'
                      : 'Gunakan Markdown untuk memformat konten'}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {previewMode ? (
                    <div className="prose max-w-none">
                      {currentChapter?.content ? (
                        <div className="whitespace-pre-wrap">
                          {currentChapter.content}
                        </div>
                      ) : (
                        <p className="text-muted-foreground italic">
                          Belum ada konten untuk chapter ini
                        </p>
                      )}
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <DynamicContentEditor
                        initialContent={currentChapter?.content || []}
                        onContentChange={updateChapterContent}
                      />
                      <div className="flex justify-between items-center text-sm text-muted-foreground">
                        <span>Mendukung Markdown formatting</span>
                        <span>
                          {currentChapter?.content?.length || 0} karakter
                        </span>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </div>

      {/* Quiz Dialog */}
      <Dialog open={isQuizDialogOpen} onOpenChange={setIsQuizDialogOpen}>
        <DialogContent className="sm:max-w-4xl max-h-[80vh] overflow-y-auto p-6">
          <DialogHeader>
            <DialogTitle>
              {editingQuiz.quiz?.questions.length ? 'Edit Quiz' : 'Buat Quiz Baru'}
            </DialogTitle>
            <DialogDescription>
              Buat pertanyaan untuk menguji pemahaman siswa
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-6">
            {/* Quiz Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="quizName">Nama Quiz *</Label>
                <Input
                  id="quizName"
                  placeholder="Masukkan nama quiz"
                  value={editingQuiz.quiz?.name || ''}
                  onChange={(e) => setEditingQuiz(prev => ({
                    ...prev,
                    quiz: prev.quiz ? { ...prev.quiz, name: e.target.value } : null
                  }))}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="minimumScore">Nilai Minimum (%)</Label>
                <Input
                  id="minimumScore"
                  type="number"
                  min="0"
                  max="100"
                  value={editingQuiz.quiz?.minimumScore || 70}
                  onChange={(e) => setEditingQuiz(prev => ({
                    ...prev,
                    quiz: prev.quiz ? { ...prev.quiz, minimumScore: parseInt(e.target.value) } : null
                  }))}
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="quizDescription">Deskripsi</Label>
              <Textarea
                id="quizDescription"
                placeholder="Jelaskan tentang quiz ini..."
                value={editingQuiz.quiz?.description || ''}
                onChange={(e) => setEditingQuiz(prev => ({
                  ...prev,
                  quiz: prev.quiz ? { ...prev.quiz, description: e.target.value } : null
                }))}
                rows={2}
              />
            </div>

            {/* Questions */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="text-lg font-semibold">Pertanyaan</h4>
                <Button onClick={createQuestion}>
                  <Plus className="w-4 h-4 mr-2" />
                  Tambah Pertanyaan
                </Button>
              </div>
              
              {editingQuiz.quiz?.questions.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <HelpCircle className="w-8 h-8 mx-auto mb-2" />
                  <p className="text-sm">Belum ada pertanyaan</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {editingQuiz.quiz?.questions.map((question, index) => (
                    <Card key={question.id}>
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-2">
                              <Badge variant="outline">{index + 1}</Badge>
                              <Badge variant="secondary">
                                {question.type === 'multiple_choice' ? 'Pilihan Ganda' :
                                 question.type === 'true_false' ? 'Benar/Salah' : 'Essay'}
                              </Badge>
                              <span className="text-sm text-muted-foreground">
                                {question.points} poin
                              </span>
                            </div>
                            <div className="text-sm">
                              {question.question.map((block, blockIndex) => (
                                <React.Fragment key={blockIndex}>
                                  {block.type === 'text' && <p>{block.value}</p>}
                                  {block.type === 'image' && block.value && (
                                    <img src={block.value} alt={`Question image ${blockIndex}`} className="max-w-xs max-h-32 object-contain mt-2" />
                                  )}
                                </React.Fragment>
                              ))}
                            </div>
                            {question.type === 'multiple_choice' && question.options && (
                              <div className="mt-2 space-y-1">
                                {question.options.map((option, optIndex) => (
                                  <div key={optIndex} className="text-xs text-muted-foreground">
                                    {String.fromCharCode(65 + optIndex)}.
                                    {option.content.map((block, optionBlockIndex) => (
                                      <React.Fragment key={optionBlockIndex}>
                                        {block.type === 'text' && <span>{block.value}</span>}
                                        {block.type === 'image' && block.value && (
                                          <img src={block.value} alt={`Option image ${optionBlockIndex}`} className="inline-block max-h-8 object-contain ml-1" />
                                        )}
                                      </React.Fragment>
                                    ))}
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>
                          <div className="flex items-center space-x-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => editQuestion(question)}
                            >
                              <Edit className="w-4 h-4" />
                            </Button>
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <Trash2 className="w-4 h-4" />
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Hapus Pertanyaan</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    Apakah Anda yakin ingin menghapus pertanyaan ini?
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Batal</AlertDialogCancel>
                                  <AlertDialogAction onClick={() => deleteQuestion(question.id)}>
                                    Hapus
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsQuizDialogOpen(false)}>
              Batal
            </Button>
            <Button onClick={saveQuiz}>
              <Save className="w-4 h-4 mr-2" />
              Simpan Quiz
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Question Dialog */}
      <Dialog open={isQuestionDialogOpen} onOpenChange={setIsQuestionDialogOpen}>
        <DialogContent className="sm:max-w-2xl max-h-[80vh] overflow-y-auto p-6">
          <DialogHeader>
            <DialogTitle>
              {editingQuestion?.question ? 'Edit Pertanyaan' : 'Tambah Pertanyaan Baru'}
            </DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="questionType">Tipe Pertanyaan</Label>
                <Select 
                  value={editingQuestion?.type || 'multiple_choice'}
                  onValueChange={(value: 'multiple_choice' | 'true_false' | 'essay') => {
                    setEditingQuestion(prev => {
                      if (!prev) return null;
                      const newQuestion = { ...prev, type: value };
                      if (value === 'true_false') {
                        newQuestion.options = [
                          { content: [{ type: 'text', value: 'True' }], isCorrect: false },
                          { content: [{ type: 'text', value: 'False' }], isCorrect: false }
                        ];
                      } else if (value === 'multiple_choice') {
                        newQuestion.options = [
                          { content: [{ type: 'text', value: '' }], isCorrect: false },
                          { content: [{ type: 'text', value: '' }], isCorrect: false },
                          { content: [{ type: 'text', value: '' }], isCorrect: false },
                          { content: [{ type: 'text', value: '' }], isCorrect: false }
                        ];
                      } else {
                        newQuestion.options = undefined; // Clear options for essay
                      }
                      return newQuestion;
                    });
                  }}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="multiple_choice">Pilihan Ganda</SelectItem>
                    <SelectItem value="true_false">Benar/Salah</SelectItem>
                    <SelectItem value="essay">Essay</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="questionPoints">Poin</Label>
                <Input
                  id="questionPoints"
                  type="number"
                  min="1"
                  value={editingQuestion?.points || 1}
                  onChange={(e) => setEditingQuestion(prev => 
                    prev ? { ...prev, points: parseInt(e.target.value) } : null
                  )}
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="questionText">Pertanyaan *</Label>
              <DynamicContentEditor
                initialContent={editingQuestion?.question || []}
                onContentChange={(content) => setEditingQuestion(prev =>
                  prev ? { ...prev, question: content } : null
                )}
                allowImages={true} // Allow images in questions
              />
            </div>
            
            {(editingQuestion?.type === 'multiple_choice' || editingQuestion?.type === 'true_false') && (
              <div className="space-y-4">
                <Label>Pilihan Jawaban</Label>
                {editingQuestion.options?.map((option, index) => (
                  <div key={index} className="flex flex-col space-y-2 border p-3 rounded-md">
                    <div className="flex items-center space-x-2">
                      {editingQuestion.type === 'multiple_choice' && (
                        <span className="text-sm font-medium w-6">
                          {String.fromCharCode(65 + index)}.
                        </span>
                      )}
                      {editingQuestion.type === 'multiple_choice' ? (
                        <DynamicContentEditor
                          initialContent={option.content || []}
                          onContentChange={(content) => {
                            const newOptions = [...(editingQuestion.options || [])];
                            newOptions[index] = { ...newOptions[index], content: content };
                            setEditingQuestion(prev =>
                              prev ? { ...prev, options: newOptions } : null
                            );
                          }}
                          allowImages={true} // Allow images in options
                        />
                      ) : (
                        <span className="text-base font-medium">{option.content[0].value}</span>
                      )}
                    </div>
                    <div className="flex items-center space-x-2 mt-2">
                      <Checkbox
                        id={`option-correct-${index}`}
                        checked={option.isCorrect}
                        onCheckedChange={(checked: boolean) => {
                          const newOptions = [...(editingQuestion.options || [])];
                          newOptions[index] = { ...newOptions[index], isCorrect: checked as boolean };
                          setEditingQuestion(prev =>
                            prev ? { ...prev, options: newOptions } : null
                          );
                        }}
                      />
                      <Label htmlFor={`option-correct-${index}`}>Jawaban Benar</Label>
                    </div>
                  </div>
                ))}
              </div>
            )}
            
            
            {editingQuestion && editingQuestion.type === 'essay' && (
              <div className="space-y-2">
                <Label htmlFor="essay-answer">Jawaban Esai</Label>
                <Textarea
                  id="essay-answer"
                  placeholder="Masukkan jawaban esai untuk pertanyaan ini"
                  value={editingQuestion.essayAnswer || ''}
                  onChange={(e) => setEditingQuestion(prev =>
                    prev ? { ...prev, essayAnswer: e.target.value } : null
                  )}
                  rows={4}
                />
              </div>
            )}

            {editingQuestion && (
              <div className="space-y-2">
                <Label htmlFor="explanation">Penjelasan Jawaban (Opsional)</Label>
                <DynamicContentEditor
                  initialContent={editingQuestion?.explanation || []}
                  onContentChange={(content) => {
                    setEditingQuestion(prev =>
                      prev ? { ...prev, explanation: content } : null
                    );
                  }}
                  placeholder="Jelaskan jawaban yang benar atau berikan informasi tambahan"
                  allowImages={true}
                />
              </div>
            )}
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsQuestionDialogOpen(false)}>
              Batal
            </Button>
            <Button onClick={saveQuestion}>
              {editingQuestion?.question ? 'Perbarui' : 'Tambah'} Pertanyaan
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}