'use client';

import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { ArrowLef<PERSON>, Sparkles } from 'lucide-react';
import { toast } from 'sonner';
import { useAuth } from '@/hooks/use-auth';
import Link from 'next/link';
import { CourseCreationWizard, CourseData } from '@/components/course/course-creation-wizard';
import { useState } from 'react';

export default function NewCoursePage() {
  const router = useRouter();
  const { user } = useAuth();
  const [isUploading, setIsUploading] = useState(false);

  const uploadImageToBlob = async (file: File): Promise<string | null> => {
    try {
      setIsUploading(true);
      
      const formData = new FormData();
      formData.append('file', file);
      
      const response = await fetch(`/api/upload?filename=${file.name}`, {
        method: 'POST',
        body: file,
      });
      
      if (!response.ok) {
        throw new Error('Failed to upload image');
      }
      
      const data = await response.json();
      return data.url;
    } catch (error) {
      console.error('Error uploading image:', error);
      toast.error('Failed to upload cover image');
      return null;
    } finally {
      setIsUploading(false);
    }
  };
  const handleCourseCreate = async (courseData: CourseData) => {
    if (!user) {
      toast.error('Please log in to create courses');
      return;
    }

    try {
      let coverPictureUrl = null;
      
      // Step 1: Upload cover image if exists
      if (courseData.coverImage) {
        toast.loading('Uploading cover image...', { id: 'course-creation' });
        coverPictureUrl = await uploadImageToBlob(courseData.coverImage);
        if (!coverPictureUrl) {
          return; // Upload failed, stop course creation
        }
      }
      
      // Step 2: Create the basic course
      toast.loading('Creating course...', { id: 'course-creation' });
      
      const courseResponse = await fetch('/api/courses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: courseData.name,
          description: courseData.description,
          courseCode: courseData.courseCode,
          type: courseData.type,
          startDate: courseData.startDate,
          endDate: courseData.endDate,
          teacherId: user.id,
          institutionId: user.institutionId,
          coverPicture: coverPictureUrl,
          isPurchasable: courseData.isPurchasable,
          price: courseData.price,
          currency: courseData.currency,
          previewMode: courseData.previewMode,
          admissions: courseData.admissions,
          academics: courseData.academics,
          tuitionAndFinancing: courseData.tuitionAndFinancing,
          careers: courseData.careers,
          studentExperience: courseData.studentExperience
        })
      });

      const courseResult = await courseResponse.json();

      if (!courseResult.success) {
        throw new Error(courseResult.error || 'Failed to create course');
      }

      const createdCourse = courseResult.course;
      toast.loading('Creating modules...', { id: 'course-creation' });

      // Step 3: Create modules sequentially
      const createdModules = [];
      for (let i = 0; i < courseData.modules.length; i++) {
        const moduleData = courseData.modules[i];
        
        const moduleResponse = await fetch('/api/modules', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            name: moduleData.name,
            description: moduleData.description,
            courseId: createdCourse.id,
            teacherId: user.id,
            orderIndex: moduleData.orderIndex
          })
        });

        const moduleResult = await moduleResponse.json();
        if (!moduleResult.module) {
          throw new Error(`Failed to create module: ${moduleData.name}`);
        }

        createdModules.push({ ...moduleResult.module, originalData: moduleData });
      }

      toast.loading('Creating chapters...', { id: 'course-creation' });

      // Step 4: Create chapters for each module
      const createdChapters = [];
      for (const createdModule of createdModules) {
        const moduleData = createdModule.originalData;
        
        for (let j = 0; j < moduleData.chapters.length; j++) {
          const chapterData = moduleData.chapters[j];
          
          const chapterResponse = await fetch('/api/chapters', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              name: chapterData.name,
              content: chapterData.content,
              moduleId: createdModule.id,
              teacherId: user.id,
              orderIndex: chapterData.orderIndex
            })
          });

          const chapterResult = await chapterResponse.json();
          if (!chapterResult.chapter) {
            throw new Error(`Failed to create chapter: ${chapterData.name}`);
          }

          createdChapters.push({ ...chapterResult.chapter, originalData: chapterData });
        }
      }

      // Step 5: Create quizzes if they exist
      if (courseData.modules.some(m => m.chapters.some(c => c.hasChapterQuiz && c.chapterQuiz))) {
        toast.loading('Creating quizzes...', { id: 'course-creation' });
        
        for (const createdChapter of createdChapters) {
          const chapterData = createdChapter.originalData;
          
          if (chapterData.hasChapterQuiz && chapterData.chapterQuiz) {
            const quizData = chapterData.chapterQuiz;
            
            const quizResponse = await fetch('/api/quizzes', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                name: quizData.name,
                description: quizData.description,
                quizType: 'chapter',
                timeLimit: quizData.timeLimit,
                minimumScore: quizData.minimumScore,
                chapterId: createdChapter.id,
                teacherId: user.id,
                questions: quizData.questions.map((q: any) => ({
                  type: q.type,
                  question: q.question,
                  options: q.options,
                  essayAnswer: q.essayAnswer, // Changed from correctAnswer
                  explanation: q.explanation, // Added explanation
                  points: q.points,
                  orderIndex: q.orderIndex
                }))
              })
            });

            const quizResult = await quizResponse.json();
            if (!quizResult.quiz) {
              console.warn(`Failed to create quiz for chapter: ${chapterData.name}`);
            }
          }
        }
      }

      // Step 6: Create module quizzes if they exist
      if (courseData.modules.some(m => m.hasModuleQuiz && m.moduleQuiz)) {
        toast.loading('Creating module quizzes...', { id: 'course-creation' });
        
        for (const createdModule of createdModules) {
          const moduleData = createdModule.originalData;
          
          if (moduleData.hasModuleQuiz && moduleData.moduleQuiz) {
            const quizData = moduleData.moduleQuiz;
            
            const quizResponse = await fetch('/api/quizzes', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                name: quizData.name,
                description: quizData.description,
                quizType: 'module',
                timeLimit: quizData.timeLimit,
                minimumScore: quizData.minimumScore,
                moduleId: createdModule.id, // Use moduleId for module quiz
                teacherId: user.id,
                questions: quizData.questions.map((q: any) => ({
                   type: q.type,
                   question: q.question,
                   options: q.options,
                   essayAnswer: q.essayAnswer, // Changed from correctAnswer
                   explanation: q.explanation, // Added explanation
                   points: q.points,
                   orderIndex: q.orderIndex
                 }))
              })
            });

            const quizResult = await quizResponse.json();
            if (!quizResult.quiz) {
              console.warn(`Failed to create module quiz: ${quizData.name}`);
            }
          }
        }
      }

      // Step 7: Create final exam if it exists
      if (courseData.finalExam) {
        toast.loading('Creating final exam...', { id: 'course-creation' });
        
        const finalExam = courseData.finalExam;
        
        const examResponse = await fetch('/api/quizzes', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            name: finalExam.name,
            description: finalExam.description,
            quizType: 'final',
            timeLimit: finalExam.timeLimit,
            minimumScore: finalExam.minimumScore,
            courseId: createdCourse.id, // Use courseId for final exam
            teacherId: user.id,
            questions: finalExam.questions.map((q: any) => ({
               type: q.type,
               question: q.question,
               options: q.options,
               essayAnswer: q.essayAnswer, // Changed from correctAnswer
               explanation: q.explanation, // Added explanation
               points: q.points,
               orderIndex: q.orderIndex
             }))
          })
        });

        const examResult = await examResponse.json();
        if (!examResult.quiz) {
          console.warn('Failed to create final exam');
        }
      }

      toast.success('Course created successfully with all modules, chapters, and quizzes!', { id: 'course-creation' });
      router.push('/dashboard/teacher/courses');
      
    } catch (error) {
      console.error('Error creating course:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to create course', { id: 'course-creation' });
    }
  };

  return (
    <div className='space-y-6'>
      <div className='flex items-center justify-between'>
        <div className='flex items-center space-x-4'>
          <Link href='/dashboard/teacher/courses'>
            <Button variant='outline' size='sm'>
              <ArrowLeft className='mr-2 h-4 w-4' />
              Back
            </Button>
          </Link>
          <div>
            <h1 className='text-3xl font-bold tracking-tight'>
              Create New Course
            </h1>
            <p className='text-muted-foreground'>
              Create a comprehensive course with our step-by-step wizard
            </p>
          </div>
        </div>
        
        <Link href='/dashboard/teacher/courses/generate'>
          <Button variant='outline'>
            <Sparkles className='mr-2 h-4 w-4' />
            Try AI Generator
          </Button>
        </Link>
      </div>

      <CourseCreationWizard 
        onComplete={handleCourseCreate}
        onCancel={() => router.push('/dashboard/teacher/courses')}
      />
    </div>
  );
}
