import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { courses, users, modules, chapters, quizzes, questions, quizAttempts, courseEnrollments, studentEnrollments } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';

// GET /api/courses/[id] - Get a specific course with details
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const courseId = parseInt(id);
    
    if (isNaN(courseId)) {
      return NextResponse.json({ error: 'Invalid course ID' }, { status: 400 });
    }

    // Get course with teacher information
    const courseData = await db
      .select({
        id: courses.id,
        name: courses.name,
        description: courses.description,
        type: courses.type,
        startDate: courses.startDate,
        endDate: courses.endDate,
        teacherId: courses.teacherId,
        institutionId: courses.institutionId,
        courseCode: courses.courseCode,
        coverPicture: courses.coverPicture,
        createdAt: courses.createdAt,
        updatedAt: courses.updatedAt,
        teacherName: users.name,
        teacherEmail: users.email
      })
      .from(courses)
      .leftJoin(users, eq(courses.teacherId, users.id))
      .where(eq(courses.id, courseId))
      .limit(1);

    if (courseData.length === 0) {
      return NextResponse.json({ error: 'Course not found' }, { status: 404 });
    }

    const course = courseData[0];

    // Get modules for this course
    const courseModules = await db
      .select()
      .from(modules)
      .where(eq(modules.courseId, courseId));

    // Get chapters for each module
    const modulesWithChapters = await Promise.all(
      courseModules.map(async (module) => {
        const moduleChapters = await db
          .select()
          .from(chapters)
          .where(eq(chapters.moduleId, module.id));

        // Get quizzes for each chapter
        const chaptersWithQuizzes = await Promise.all(
          moduleChapters.map(async (chapter) => {
            const chapterQuizzes = await db
              .select()
              .from(quizzes)
              .where(eq(quizzes.chapterId, chapter.id));

            return {
              ...chapter,
              quizzes: chapterQuizzes
            };
          })
        );

        return {
          ...module,
          chapters: chaptersWithQuizzes
        };
      })
    );

    // Get enrollment statistics
    const enrollmentStats = await db
      .select({ count: courseEnrollments.id })
      .from(courseEnrollments)
      .where(eq(courseEnrollments.courseId, courseId));

    const studentStats = await db
      .select({ count: studentEnrollments.id })
      .from(studentEnrollments)
      .where(eq(studentEnrollments.courseId, courseId));

    return NextResponse.json({
      success: true,
      course: {
        ...course,
        modules: modulesWithChapters,
        enrollmentCount: enrollmentStats.length,
        studentCount: studentStats.length
      }
    });
  } catch (error) {
    console.error('Error fetching course:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/courses/[id] - Update a course
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const courseId = parseInt(id);
    
    if (isNaN(courseId)) {
      return NextResponse.json({ error: 'Invalid course ID' }, { status: 400 });
    }

    const body = await request.json();
    const {
      name,
      description,
      type,
      startDate,
      endDate,
      teacherId,
      courseCode,
      coverPicture
    } = body;

    // Check if course exists
    const existingCourse = await db
      .select()
      .from(courses)
      .where(eq(courses.id, courseId))
      .limit(1);

    if (existingCourse.length === 0) {
      return NextResponse.json({ error: 'Course not found' }, { status: 404 });
    }

    // If teacherId is provided, verify the teacher exists and has permission
    if (teacherId) {
      const teacher = await db
        .select()
        .from(users)
        .where(
          and(
            eq(users.id, teacherId),
            eq(users.role, 'teacher')
          )
        )
        .limit(1);

      if (teacher.length === 0) {
        return NextResponse.json(
          { error: 'Teacher not found or not authorized' },
          { status: 403 }
        );
      }
    }

    // If courseCode is being updated, check uniqueness
    if (courseCode && courseCode !== existingCourse[0].courseCode) {
      const codeExists = await db
        .select()
        .from(courses)
        .where(
          and(
            eq(courses.courseCode, courseCode),
            eq(courses.id, courseId) // Exclude current course
          )
        )
        .limit(1);

      if (codeExists.length > 0) {
        return NextResponse.json(
          { error: 'Course code already exists' },
          { status: 400 }
        );
      }
    }

    // Update the course
    const updatedCourse = await db
      .update(courses)
      .set({
        ...(name && { name }),
        ...(description && { description }),
        ...(type && { type }),
        ...(startDate && { startDate: new Date(startDate) }),
        ...(endDate && { endDate: new Date(endDate) }),
        ...(teacherId && { teacherId }),
        ...(courseCode && { courseCode }),
        ...(coverPicture && { coverPicture }),
        updatedAt: new Date()
      })
      .where(eq(courses.id, courseId))
      .returning();

    return NextResponse.json({
      course: updatedCourse[0],
      message: 'Course updated successfully'
    });
  } catch (error) {
    console.error('Error updating course:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE /api/courses/[id] - Delete a course
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const courseId = parseInt(id);
    
    if (isNaN(courseId)) {
      return NextResponse.json({ error: 'Invalid course ID' }, { status: 400 });
    }

    const searchParams = request.nextUrl.searchParams;
    const teacherId = searchParams.get('teacherId');

    // Check if course exists
    const existingCourse = await db
      .select()
      .from(courses)
      .where(eq(courses.id, courseId))
      .limit(1);

    if (existingCourse.length === 0) {
      return NextResponse.json({ error: 'Course not found' }, { status: 404 });
    }

    // Verify teacher has permission to delete this course
    if (teacherId && existingCourse[0].teacherId !== parseInt(teacherId)) {
      return NextResponse.json(
        { error: 'Not authorized to delete this course' },
        { status: 403 }
      );
    }

    // Delete related data in correct order (due to foreign key constraints)
    // 1. Get all quizzes for this course (chapter, module, and final exam quizzes)
    const allQuizzes = await db
      .select({ id: quizzes.id })
      .from(quizzes)
      .where(
        and(
          eq(quizzes.courseId, courseId)
        )
      );

    // Also get quizzes from chapters and modules
    const courseModules = await db
      .select({ id: modules.id })
      .from(modules)
      .where(eq(modules.courseId, courseId));

    const moduleQuizzes = await db
      .select({ id: quizzes.id })
      .from(quizzes)
      .innerJoin(modules, eq(quizzes.moduleId, modules.id))
      .where(eq(modules.courseId, courseId));

    const chapterQuizzes = [];
    for (const moduleRecord of courseModules) {
      const moduleChapters = await db
        .select({ id: chapters.id })
        .from(chapters)
        .where(eq(chapters.moduleId, moduleRecord.id));

      for (const chapter of moduleChapters) {
        const chapterQuizzesForChapter = await db
          .select({ id: quizzes.id })
          .from(quizzes)
          .where(eq(quizzes.chapterId, chapter.id));
        chapterQuizzes.push(...chapterQuizzesForChapter);
      }
    }

    // Combine all quiz IDs
    const allQuizIds = [
      ...allQuizzes.map(q => q.id),
      ...moduleQuizzes.map(q => q.id),
      ...chapterQuizzes.map(q => q.id)
    ];

    // 2. Delete questions and quiz attempts for all quizzes
    for (const quizId of allQuizIds) {
      // Delete questions first
      await db.delete(questions).where(eq(questions.quizId, quizId));
      // Delete quiz attempts
      await db.delete(quizAttempts).where(eq(quizAttempts.quizId, quizId));
    }

    // 3. Delete all quizzes
    for (const quizId of allQuizIds) {
      await db.delete(quizzes).where(eq(quizzes.id, quizId));
    }

    // 4. Delete chapters
    for (const moduleRecord of courseModules) {
      await db.delete(chapters).where(eq(chapters.moduleId, moduleRecord.id));
    }

    // 5. Delete modules
    await db.delete(modules).where(eq(modules.courseId, courseId));

    // 6. Delete enrollments
    await db.delete(courseEnrollments).where(eq(courseEnrollments.courseId, courseId));
    await db.delete(studentEnrollments).where(eq(studentEnrollments.courseId, courseId));

    // 7. Finally delete the course
    await db.delete(courses).where(eq(courses.id, courseId));

    return NextResponse.json({success: true, message: 'Course deleted successfully' });
  } catch (error) {
    console.error('Error deleting course:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}