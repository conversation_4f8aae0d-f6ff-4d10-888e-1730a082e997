import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import {
  courses,
  users,
  modules,
  courseAdmissions,
  courseAcademics,
  courseTuitionAndFinancing,
  courseCareers,
  courseStudentExperience
} from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';

// Generate a random course code
function generateCourseCode(): string {
  const prefix = 'COURSE';
  const randomNum = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
  return `${prefix}${randomNum}`;
}

// GET /api/courses - Get courses (for teachers or public access for students)
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const teacherId = searchParams.get('teacherId');
    const institutionId = searchParams.get('institutionId');
    const publicAccess = searchParams.get('public'); // For student access to all published courses

    // If public access is requested, return all published courses
    if (publicAccess === 'true') {
      // Get all published courses with teacher information
      const publicCourses = await db
        .select({
          id: courses.id,
          name: courses.name,
          description: courses.description,
          instructor: courses.instructor,
          type: courses.type,
          enrollmentType: courses.enrollmentType,
          startDate: courses.startDate,
          endDate: courses.endDate,
          teacherId: courses.teacherId,
          institutionId: courses.institutionId,
          courseCode: courses.courseCode,
          coverPicture: courses.coverPicture,
          isPurchasable: courses.isPurchasable,
          price: courses.price,
          currency: courses.currency,
          previewMode: courses.previewMode,
          createdAt: courses.createdAt,
          updatedAt: courses.updatedAt,
          teacherName: users.name,
          teacherEmail: users.email
        })
        .from(courses)
        .leftJoin(users, eq(courses.teacherId, users.id));

      // Get module count and additional data for each course
      const coursesWithCounts = await Promise.all(
        publicCourses.map(async (course) => {
          const moduleCount = await db
            .select({ count: modules.id })
            .from(modules)
            .where(eq(modules.courseId, course.id));

          // Get admissions data
          const admissionsData = await db
            .select()
            .from(courseAdmissions)
            .where(eq(courseAdmissions.courseId, course.id))
            .limit(1);

          // Get academics data
          const academicsData = await db
            .select()
            .from(courseAcademics)
            .where(eq(courseAcademics.courseId, course.id))
            .limit(1);

          // Get tuition and financing data
          const tuitionData = await db
            .select()
            .from(courseTuitionAndFinancing)
            .where(eq(courseTuitionAndFinancing.courseId, course.id))
            .limit(1);

          // Get careers data
          const careersData = await db
            .select()
            .from(courseCareers)
            .where(eq(courseCareers.courseId, course.id))
            .limit(1);

          // Get student experience data
          const studentExperienceData = await db
            .select()
            .from(courseStudentExperience)
            .where(eq(courseStudentExperience.courseId, course.id))
            .limit(1);

          return {
            id: course.id.toString(),
            name: course.name,
            code: course.courseCode || '',
            description: course.description || '',
            instructor: course.instructor || course.teacherName || 'Unknown Instructor',
            startDate: course.startDate?.toISOString() || new Date().toISOString(),
            endDate: course.endDate?.toISOString() || new Date().toISOString(),
            enrollmentType: course.enrollmentType || 'code' as const,
            enrollmentCode: course.courseCode,
            isPurchasable: course.isPurchasable || false,
            price: course.price ? parseFloat(course.price) : 0,
            currency: course.currency || 'IDR',
            previewMode: course.previewMode || false,
            thumbnail: course.coverPicture || undefined,
            admissions: admissionsData[0] ? {
              requirements: admissionsData[0].requirements as string[] || [],
              applicationDeadline: admissionsData[0].applicationDeadline || undefined,
              prerequisites: admissionsData[0].prerequisites as string[] || []
            } : undefined,
            academics: academicsData[0] ? {
              credits: academicsData[0].credits || 0,
              workload: academicsData[0].workload || '',
              assessment: academicsData[0].assessment as string[] || []
            } : undefined,
            tuitionAndFinancing: tuitionData[0] ? {
              totalCost: tuitionData[0].totalCost ? parseFloat(tuitionData[0].totalCost) : 0,
              paymentOptions: tuitionData[0].paymentOptions as string[] || [],
              scholarships: tuitionData[0].scholarships as string[] || []
            } : undefined,
            careers: careersData[0] ? {
              outcomes: careersData[0].outcomes as string[] || [],
              industries: careersData[0].industries as string[] || [],
              averageSalary: careersData[0].averageSalary || undefined
            } : undefined,
            studentExperience: studentExperienceData[0] ? {
              testimonials: studentExperienceData[0].testimonials as { name: string; feedback: string }[] || [],
              facilities: studentExperienceData[0].facilities as string[] || [],
              support: studentExperienceData[0].support as string[] || []
            } : undefined,
            modules: [], // Will be populated when needed
            finalExam: {
              id: 'final-exam',
              title: 'Final Exam',
              type: 'final' as const,
              questions: [],
              minimumScore: 70,
              attempts: 0,
              maxAttempts: 3,
              isPassed: false
            },
            certificate: {
              isEligible: false,
              isGenerated: false
            },
            minPassingScore: 70,
            totalProgress: 0,
            status: 'not-started' as const,
            // Additional fields for compatibility
            moduleCount: moduleCount.length,
            studentCount: 0
          };
        })
      );

      return NextResponse.json({ success: true, courses: coursesWithCounts });
    }

    // Original teacher-specific logic
    if (!teacherId) {
      return NextResponse.json({ error: 'Teacher ID required' }, { status: 400 });
    }

    let whereCondition;
    if (institutionId) {
      // Get all courses for the institution (for super admin)
      whereCondition = eq(courses.institutionId, parseInt(institutionId));
    } else {
      // Get courses for the specific teacher
      whereCondition = eq(courses.teacherId, parseInt(teacherId));
    }

    // Get courses with teacher information
    const teacherCourses = await db
      .select({
        id: courses.id,
        name: courses.name,
        description: courses.description,
        instructor: courses.instructor,
        type: courses.type,
        enrollmentType: courses.enrollmentType,
        startDate: courses.startDate,
        endDate: courses.endDate,
        teacherId: courses.teacherId,
        institutionId: courses.institutionId,
        courseCode: courses.courseCode,
        coverPicture: courses.coverPicture,
        isPurchasable: courses.isPurchasable,
        price: courses.price,
        currency: courses.currency,
        previewMode: courses.previewMode,
        createdAt: courses.createdAt,
        updatedAt: courses.updatedAt,
        teacherName: users.name,
        teacherEmail: users.email
      })
      .from(courses)
      .leftJoin(users, eq(courses.teacherId, users.id))
      .where(whereCondition);

    // Get module count for each course
    const coursesWithCounts = await Promise.all(
      teacherCourses.map(async (course) => {
        const moduleCount = await db
          .select({ count: modules.id })
          .from(modules)
          .where(eq(modules.courseId, course.id));

        return {
          ...course,
          moduleCount: moduleCount.length,
          studentCount: 0, // TODO: Calculate actual student count from enrollments
          status: 'published' // TODO: Add status field to schema or calculate based on dates
        };
      })
    );

    return NextResponse.json({ success: true, courses: coursesWithCounts });
  } catch (error) {
    console.error('Error fetching courses:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/courses - Create a new course
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      name,
      description,
      instructor,
      type = 'self_paced',
      enrollmentType = 'code',
      startDate,
      endDate,
      teacherId,
      institutionId,
      courseCode,
      coverPicture,
      isPurchasable,
      price,
      currency,
      previewMode,
      admissions,
      academics,
      tuitionAndFinancing,
      careers,
      studentExperience
    } = body;

    // Validate required fields
    if (!name || !teacherId || !institutionId) {
      return NextResponse.json(
        { error: 'Name, teacher ID, and institution ID are required' },
        { status: 400 }
      );
    }

    // Verify teacher exists and belongs to the institution
    const teacher = await db
      .select()
      .from(users)
      .where(
        and(
          eq(users.id, teacherId),
          eq(users.institutionId, institutionId),
          eq(users.role, 'teacher')
        )
      )
      .limit(1);

    if (teacher.length === 0) {
      return NextResponse.json(
        { error: 'Teacher not found or not authorized' },
        { status: 403 }
      );
    }

    // Generate course code if not provided
    let finalCourseCode = courseCode;
    if (!finalCourseCode) {
      let isUnique = false;
      while (!isUnique) {
        finalCourseCode = generateCourseCode();
        const existing = await db
          .select()
          .from(courses)
          .where(eq(courses.courseCode, finalCourseCode))
          .limit(1);
        isUnique = existing.length === 0;
      }
    } else {
      // Check if provided course code is unique
      const existing = await db
        .select()
        .from(courses)
        .where(eq(courses.courseCode, finalCourseCode))
        .limit(1);
      
      if (existing.length > 0) {
        return NextResponse.json(
          { error: 'Course code already exists' },
          { status: 400 }
        );
      }
    }

    // Create the course
    const courseInsertResult = await db
      .insert(courses)
      .values({
        name,
        description,
        instructor,
        type,
        enrollmentType,
        startDate: startDate ? new Date(startDate) : null,
        endDate: endDate ? new Date(endDate) : null,
        teacherId,
        institutionId,
        courseCode: finalCourseCode,
        coverPicture,
        isPurchasable,
        price,
        currency,
        previewMode
      })
      .returning();

    const newCourse = courseInsertResult[0];

    // Insert related course details if provided
    if (admissions) {
      await db.insert(courseAdmissions).values({
        courseId: newCourse.id,
        requirements: admissions.requirements,
        applicationDeadline: admissions.applicationDeadline,
        prerequisites: admissions.prerequisites,
      });
    }
    if (academics) {
      await db.insert(courseAcademics).values({
        courseId: newCourse.id,
        credits: academics.credits,
        workload: academics.workload,
        assessment: academics.assessment,
      });
    }
    if (tuitionAndFinancing) {
      await db.insert(courseTuitionAndFinancing).values({
        courseId: newCourse.id,
        totalCost: tuitionAndFinancing.totalCost,
        paymentOptions: tuitionAndFinancing.paymentOptions,
        scholarships: tuitionAndFinancing.scholarships,
      });
    }
    if (careers) {
      await db.insert(courseCareers).values({
        courseId: newCourse.id,
        outcomes: careers.outcomes,
        industries: careers.industries,
        averageSalary: careers.averageSalary,
      });
    }
    if (studentExperience) {
      await db.insert(courseStudentExperience).values({
        courseId: newCourse.id,
        testimonials: studentExperience.testimonials,
        facilities: studentExperience.facilities,
        support: studentExperience.support,
      });
    }

    return NextResponse.json(
      { success: true, course: newCourse, message: 'Course created successfully' },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error creating course:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}