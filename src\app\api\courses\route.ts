import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import {
  courses,
  users,
  modules,
  chapters,
  quizzes,
  courseAdmissions,
  courseAcademics,
  courseTuitionAndFinancing,
  courseCareers,
  courseStudentExperience
} from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';

// Generate a random course code
function generateCourseCode(): string {
  const prefix = 'COURSE';
  const randomNum = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
  return `${prefix}${randomNum}`;
}

// GET /api/courses - Get all courses for the current teacher
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const teacherId = searchParams.get('teacherId');
    const institutionId = searchParams.get('institutionId');
    
    if (!teacherId) {
      return NextResponse.json({ error: 'Teacher ID required' }, { status: 400 });
    }

    let whereCondition;
    if (institutionId) {
      // Get all courses for the institution (for super admin)
      whereCondition = eq(courses.institutionId, parseInt(institutionId));
    } else {
      // Get courses for the specific teacher
      whereCondition = eq(courses.teacherId, parseInt(teacherId));
    }

    // Get courses with teacher information
    const teacherCourses = await db
      .select({
        id: courses.id,
        name: courses.name,
        description: courses.description,
        type: courses.type,
        startDate: courses.startDate,
        endDate: courses.endDate,
        teacherId: courses.teacherId,
        institutionId: courses.institutionId,
        courseCode: courses.courseCode,
        coverPicture: courses.coverPicture,
        createdAt: courses.createdAt,
        updatedAt: courses.updatedAt,
        teacherName: users.name,
        teacherEmail: users.email
      })
      .from(courses)
      .leftJoin(users, eq(courses.teacherId, users.id))
      .where(whereCondition);

    // Get module count for each course
    const coursesWithCounts = await Promise.all(
      teacherCourses.map(async (course) => {
        const moduleCount = await db
          .select({ count: modules.id })
          .from(modules)
          .where(eq(modules.courseId, course.id));

        return {
          ...course,
          moduleCount: moduleCount.length,
          studentCount: 0, // TODO: Calculate actual student count from enrollments
          status: 'published' // TODO: Add status field to schema or calculate based on dates
        };
      })
    );

    return NextResponse.json({ success: true, courses: coursesWithCounts });
  } catch (error) {
    console.error('Error fetching courses:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/courses - Create a new course
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      name,
      description,
      type = 'self_paced',
      startDate,
      endDate,
      teacherId,
      institutionId,
      courseCode,
      coverPicture,
      isPurchasable,
      price,
      currency,
      previewMode,
      admissions,
      academics,
      tuitionAndFinancing,
      careers,
      studentExperience
    } = body;

    // Validate required fields
    if (!name || !teacherId || !institutionId) {
      return NextResponse.json(
        { error: 'Name, teacher ID, and institution ID are required' },
        { status: 400 }
      );
    }

    // Verify teacher exists and belongs to the institution
    const teacher = await db
      .select()
      .from(users)
      .where(
        and(
          eq(users.id, teacherId),
          eq(users.institutionId, institutionId),
          eq(users.role, 'teacher')
        )
      )
      .limit(1);

    if (teacher.length === 0) {
      return NextResponse.json(
        { error: 'Teacher not found or not authorized' },
        { status: 403 }
      );
    }

    // Generate course code if not provided
    let finalCourseCode = courseCode;
    if (!finalCourseCode) {
      let isUnique = false;
      while (!isUnique) {
        finalCourseCode = generateCourseCode();
        const existing = await db
          .select()
          .from(courses)
          .where(eq(courses.courseCode, finalCourseCode))
          .limit(1);
        isUnique = existing.length === 0;
      }
    } else {
      // Check if provided course code is unique
      const existing = await db
        .select()
        .from(courses)
        .where(eq(courses.courseCode, finalCourseCode))
        .limit(1);
      
      if (existing.length > 0) {
        return NextResponse.json(
          { error: 'Course code already exists' },
          { status: 400 }
        );
      }
    }

    // Create the course
    const courseInsertResult = await db
      .insert(courses)
      .values({
        name,
        description,
        type,
        startDate: startDate ? new Date(startDate) : null,
        endDate: endDate ? new Date(endDate) : null,
        teacherId,
        institutionId,
        courseCode: finalCourseCode,
        coverPicture,
        isPurchasable,
        price,
        currency,
        previewMode
      })
      .returning();

    const newCourse = courseInsertResult[0];

    // Insert related course details if provided
    if (admissions) {
      await db.insert(courseAdmissions).values({
        courseId: newCourse.id,
        requirements: admissions.requirements,
        applicationDeadline: admissions.applicationDeadline,
        prerequisites: admissions.prerequisites,
      });
    }
    if (academics) {
      await db.insert(courseAcademics).values({
        courseId: newCourse.id,
        credits: academics.credits,
        workload: academics.workload,
        assessment: academics.assessment,
      });
    }
    if (tuitionAndFinancing) {
      await db.insert(courseTuitionAndFinancing).values({
        courseId: newCourse.id,
        totalCost: tuitionAndFinancing.totalCost,
        paymentOptions: tuitionAndFinancing.paymentOptions,
        scholarships: tuitionAndFinancing.scholarships,
      });
    }
    if (careers) {
      await db.insert(courseCareers).values({
        courseId: newCourse.id,
        outcomes: careers.outcomes,
        industries: careers.industries,
        averageSalary: careers.averageSalary,
      });
    }
    if (studentExperience) {
      await db.insert(courseStudentExperience).values({
        courseId: newCourse.id,
        testimonials: studentExperience.testimonials,
        facilities: studentExperience.facilities,
        support: studentExperience.support,
      });
    }

    return NextResponse.json(
      { success: true, course: newCourse, message: 'Course created successfully' },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error creating course:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}