import React from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { CourseData, AdmissionsData } from '../course-creation-wizard';

interface AdmissionsStepProps {
  data: CourseData;
  onUpdate: (updates: Partial<CourseData>) => void;
}

export function AdmissionsStep({ data, onUpdate }: AdmissionsStepProps) {
  const admissions = data.admissions || { requirements: [], applicationDeadline: '', prerequisites: [] };

  const handleUpdate = (field: keyof AdmissionsData, value: string | string[]) => {
    onUpdate({
      admissions: {
        ...admissions,
        [field]: value,
      },
    });
  };

  return (
    <div className="space-y-4">
      <div>
        <Label htmlFor="requirements">Persyaratan (pisahkan dengan koma)</Label>
        <Textarea
          id="requirements"
          value={admissions.requirements.join(', ')}
          onChange={(e) => handleUpdate('requirements', e.target.value.split(',').map(s => s.trim()))}
          placeholder="Contoh: <PERSON>elar Sar<PERSON>, Pengalaman kerja minimal 2 tahun"
        />
      </div>
      <div>
        <Label htmlFor="applicationDeadline">Batas Waktu Pendaftaran</Label>
        <Input
          id="applicationDeadline"
          type="text" // Could be a date picker in a real app
          value={admissions.applicationDeadline}
          onChange={(e) => handleUpdate('applicationDeadline', e.target.value)}
          placeholder="Contoh: 2024-12-31"
        />
      </div>
      <div>
        <Label htmlFor="prerequisites">Prasyarat (pisahkan dengan koma)</Label>
        <Textarea
          id="prerequisites"
          value={admissions.prerequisites.join(', ')}
          onChange={(e) => handleUpdate('prerequisites', e.target.value.split(',').map(s => s.trim()))}
          placeholder="Contoh: Penguasaan AutoCAD, Pemahaman standar SNI"
        />
      </div>
    </div>
  );
}