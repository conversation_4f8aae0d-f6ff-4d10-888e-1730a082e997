import React from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { CourseData, AcademicsData } from '../course-creation-wizard';

interface AcademicsStepProps {
  data: CourseData;
  onUpdate: (updates: Partial<CourseData>) => void;
}

export function AcademicsStep({ data, onUpdate }: AcademicsStepProps) {
  const academics = data.academics || { credits: 0, workload: '', assessment: [] };

  const handleUpdate = (field: keyof AcademicsData, value: string | number | string[]) => {
    onUpdate({
      academics: {
        ...academics,
        [field]: value,
      },
    });
  };

  return (
    <div className="space-y-4">
      <div>
        <Label htmlFor="credits">Kredit</Label>
        <Input
          id="credits"
          type="number"
          value={academics.credits}
          onChange={(e) => handleUpdate('credits', parseInt(e.target.value))}
          placeholder="Contoh: 12"
        />
      </div>
      <div>
        <Label htmlFor="workload"><PERSON><PERSON></Label>
        <Input
          id="workload"
          type="text"
          value={academics.workload}
          onChange={(e) => handleUpdate('workload', e.target.value)}
          placeholder="Contoh: 12-15 jam/minggu"
        />
      </div>
      <div>
        <Label htmlFor="assessment">Penilaian (pisahkan dengan koma)</Label>
        <Textarea
          id="assessment"
          value={academics.assessment.join(', ')}
          onChange={(e) => handleUpdate('assessment', e.target.value.split(',').map(s => s.trim()))}
          placeholder="Contoh: Ujian teori (25%), Studi kasus proyek (35%)"
        />
      </div>
    </div>
  );
}