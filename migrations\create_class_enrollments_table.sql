-- Migration: Create class_enrollments table
-- Description: Table to manage student enrollments in classes
-- Date: 2024-01-17

-- Create class_enrollments table
CREATE TABLE IF NOT EXISTS class_enrollments (
    id SERIAL PRIMARY KEY,
    student_id INTEGER NOT NULL,
    class_id INTEGER NOT NULL,
    enrolled_at TIMESTAMP NOT NULL DEFAULT NOW(),
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    
    -- Foreign key constraints
    CONSTRAINT fk_class_enrollments_student_id 
        FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_class_enrollments_class_id 
        FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
    
    -- Unique constraint to prevent duplicate enrollments
    CONSTRAINT unique_student_class_enrollment 
        UNIQUE (student_id, class_id),
    
    -- Check constraint for status values
    CONSTRAINT check_enrollment_status 
        CHECK (status IN ('active', 'inactive'))
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_class_enrollments_student_id 
    ON class_enrollments(student_id);

CREATE INDEX IF NOT EXISTS idx_class_enrollments_class_id 
    ON class_enrollments(class_id);

CREATE INDEX IF NOT EXISTS idx_class_enrollments_status 
    ON class_enrollments(status);

CREATE INDEX IF NOT EXISTS idx_class_enrollments_enrolled_at 
    ON class_enrollments(enrolled_at);

-- Create trigger to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_class_enrollments_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_class_enrollments_updated_at
    BEFORE UPDATE ON class_enrollments
    FOR EACH ROW
    EXECUTE FUNCTION update_class_enrollments_updated_at();

-- Insert sample data (optional - remove if not needed)
-- INSERT INTO class_enrollments (student_id, class_id, status) VALUES
-- (1, 1, 'active'),
-- (2, 1, 'active'),
-- (3, 2, 'active');

-- Grant permissions (adjust based on your user roles)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON class_enrollments TO your_app_user;
-- GRANT USAGE, SELECT ON SEQUENCE class_enrollments_id_seq TO your_app_user;