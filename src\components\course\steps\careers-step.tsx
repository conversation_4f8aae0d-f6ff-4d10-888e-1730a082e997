import React from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { CourseData, CareersData } from '../course-creation-wizard';

interface CareersStepProps {
  data: CourseData;
  onUpdate: (updates: Partial<CourseData>) => void;
}

export function CareersStep({ data, onUpdate }: CareersStepProps) {
  const careers = data.careers || { outcomes: [], industries: [], averageSalary: '' };

  const handleUpdate = (field: keyof CareersData, value: string | string[]) => {
    onUpdate({
      careers: {
        ...careers,
        [field]: value,
      },
    });
  };

  return (
    <div className="space-y-4">
      <div>
        <Label htmlFor="outcomes">Hasil (pisahkan dengan koma)</Label>
        <Textarea
          id="outcomes"
          value={careers.outcomes.join(', ')}
          onChange={(e) => handleUpdate('outcomes', e.target.value.split(',').map(s => s.trim()))}
          placeholder="Contoh: Sertifikasi Arsitek Profesional Indonesia (IAI), Kompetensi perencanaan bangunan tinggi"
        />
      </div>
      <div>
        <Label htmlFor="industries">Industri (pisahkan dengan koma)</Label>
        <Textarea
          id="industries"
          value={careers.industries.join(', ')}
          onChange={(e) => handleUpdate('industries', e.target.value.split(',').map(s => s.trim()))}
          placeholder="Contoh: Firma Arsitektur, Perusahaan Konstruksi"
        />
      </div>
      <div>
        <Label htmlFor="averageSalary">Rata-rata Gaji</Label>
        <Input
          id="averageSalary"
          type="text"
          value={careers.averageSalary}
          onChange={(e) => handleUpdate('averageSalary', e.target.value)}
          placeholder="Contoh: Rp780.000.000 - Rp1.140.000.000 per tahun"
        />
      </div>
    </div>
  );
}