'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Breadcrumbs } from '@/components/breadcrumbs';
import { Badge } from '@/components/ui/badge';
import {
  BookOpen01Icon as BookOpenIcon,
  Key01Icon as KeyIcon,
  UserIcon as UsersIcon,
  Search01Icon as SearchIcon,
  Clock01Icon as ClockIcon,
  Calendar03Icon as CalendarIcon,
  ArrowRight01Icon as ArrowRightIcon,
  Settings01Icon as SettingsIcon,
  ArrowDown01Icon as ArrowDownIcon,
  CheckmarkCircle01Icon as CheckCircleIcon,
  Cancel01Icon as XCircleIcon
} from 'hugeicons-react';
import {
  architectureCourse
} from '@/constants/shared-course-data';
import { useEnrollment } from '@/contexts/enrollment-context';
import { useFeatureFlags } from '@/lib/feature-flags';
import { Course } from '@/types/lms';
import CoursePreviewCard from '@/components/lms/course-preview-card';
import CourseDetailTabs from '@/components/lms/course-detail-tabs';
import PaymentModal from '@/components/lms/payment-modal';
import CourseSuccessModal from '@/components/lms/course-success-modal';
import Link from 'next/link';

// Configuration
const SHOW_FEATURE_SETTINGS = false; // Set to true to show feature settings UI



// Enhanced course data with purchase functionality
const enhancedArchitectureCourse: Course = {
  ...architectureCourse,
  isPurchasable: true,
  price: 150000,
  currency: 'IDR',
  previewMode: true,
  thumbnail: '/assets/architecture.png',
  admissions: {
    requirements: [
      'Gelar Sarjana Arsitektur (S1) atau sederajat',
      'Pengalaman kerja minimal 2 tahun di bidang arsitektur',
      'Portofolio proyek arsitektur',
      'Surat rekomendasi dari atasan/mentor profesional'
    ],
    applicationDeadline: '2024-12-31',
    prerequisites: [
      'Penguasaan AutoCAD dan/atau software BIM',
      'Pemahaman standar SNI dan peraturan bangunan Indonesia',
      'Pengalaman dalam perencanaan dan desain arsitektur',
      'Kemampuan membaca dan membuat gambar teknik'
    ]
  },
  academics: {
    credits: 12,
    workload: '12-15 jam/minggu',
    assessment: [
      'Ujian teori (25%)',
      'Studi kasus proyek (35%)',
      'Presentasi portofolio (40%)'
    ]
  },
  tuitionAndFinancing: {
    totalCost: 6000000,
    paymentOptions: [
      'Pembayaran penuh (diskon 5%)',
      'Cicilan bulanan (3 bulan)',
      'Paket pembayaran mahasiswa tersedia'
    ],
    scholarships: [
      'Beasiswa berbasis prestasi (hingga 50% off)',
      'Diskon early bird (15% off)',
      'Diskon pendaftaran grup (10% off untuk 3+ siswa)'
    ]
  },
  careers: {
    outcomes: [
      'Sertifikasi Arsitek Profesional Indonesia (IAI)',
      'Kompetensi perencanaan bangunan tinggi',
      'Penguasaan regulasi dan standar konstruksi',
      'Kemampuan review dan supervisi proyek'
    ],
    industries: [
      'Firma Arsitektur',
      'Perusahaan Konstruksi',
      'Perencanaan Kota',
      'Desain Interior',
      'Pengembangan Real Estat'
    ],
    averageSalary: 'Rp780.000.000 - Rp1.140.000.000 per tahun'
  },
  studentExperience: {
    testimonials: [
      {
        name: 'Sarah Martinez',
        feedback: 'Program sertifikasi ini sangat membantu saya meningkatkan kompetensi profesional. Sekarang saya bisa menangani proyek yang lebih kompleks dengan percaya diri.'
      },
      {
        name: 'David Chen',
        feedback: 'Materi yang up-to-date dengan regulasi terbaru dan studi kasus yang relevan dengan kondisi Indonesia. Sangat recommended untuk arsitek yang ingin upgrade skill.'
      },
      {
        name: 'Maria Rodriguez',
        feedback: 'Dengan sertifikat IAI ini, saya bisa mengajukan izin praktik mandiri. ROI yang sangat baik untuk investasi pengembangan karier profesional.'
      }
    ],
    facilities: [
      'Platform pembelajaran online',
      'Studio desain virtual',
      'Akses perpustakaan digital',
      'Dukungan teknis 24/7',
      'Akses aplikasi mobile'
    ],
    support: [
      'Instruktur kursus khusus',
      'Forum diskusi rekan',
      'Jam kantor mingguan',
      'Layanan konseling karier',
      'Akses jaringan alumni'
    ]
  }
};

const mockCourses: Course[] = [enhancedArchitectureCourse];


const AvailableCoursesPage: React.FC = () => {
  // Context
  const { isEnrolled, enrollInCourseWithPurchase } = useEnrollment();
  const { flags, setFlag } = useFeatureFlags();

  // State management
  const [selectedCourse, setSelectedCourse] = useState<Course | null>(null);
  const [enrollmentCode, setEnrollmentCode] = useState<string>('');
  const [showEnrollModal, setShowEnrollModal] = useState<boolean>(false);
  const [showDetailModal, setShowDetailModal] = useState<boolean>(false);
  const [showPaymentModal, setShowPaymentModal] = useState<boolean>(false);
  const [showFeatureFlagsModal, setShowFeatureFlagsModal] = useState<boolean>(false);
  const [showSuccessModal, setShowSuccessModal] = useState<boolean>(false);
  const [successActionType, setSuccessActionType] = useState<'purchase' | 'enrollment'>('purchase');
  const [detailTab, setDetailTab] = useState<string>('overview');
  const [error, setError] = useState<string>('');
  const [toast, setToast] = useState<{
    show: boolean;
    message: string;
    type: 'success' | 'error' | 'loading';
  }>({
    show: false,
    message: '',
    type: 'success'
  });
  const [showScrollHint, setShowScrollHint] = useState(true);
  const [canScrollUp, setCanScrollUp] = useState(false);
  const [canScrollDown, setCanScrollDown] = useState(false);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // Scroll Detection with 200px threshold
  const checkScrollable = () => {
    const container = scrollContainerRef.current;
    if (!container) return;

    const { scrollTop, scrollHeight, clientHeight } = container;
    const scrollableHeight = scrollHeight - clientHeight;
    const SCROLL_THRESHOLD = 100; // Only show indicator if there's more than 100px to scroll
    
    const canScrollUpNow = scrollTop > 5;
    const canScrollDownNow = scrollableHeight > SCROLL_THRESHOLD && scrollTop < scrollHeight - clientHeight - 5;
    
    
    setCanScrollUp(canScrollUpNow);
    setCanScrollDown(canScrollDownNow);

    // Hide scroll hint after first interaction
    if (scrollTop > 0) {
      setShowScrollHint(false);
    }
  };

  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    // Initial check
    checkScrollable();
    
    // Check after content loads
    const timer1 = setTimeout(() => checkScrollable(), 100);
    const timer2 = setTimeout(() => checkScrollable(), 500);
    const timer3 = setTimeout(() => checkScrollable(), 1000);

    // Observe size changes
    const resizeObserver = new ResizeObserver(() => {
      checkScrollable();
    });
    resizeObserver.observe(container);

    return () => {
      clearTimeout(timer1);
      clearTimeout(timer2);
      clearTimeout(timer3);
      resizeObserver.disconnect();
    };
  }, [showDetailModal, selectedCourse]);

  // Also check when modal content changes
  useEffect(() => {
    if (showDetailModal && selectedCourse) {
      const timer = setTimeout(() => checkScrollable(), 200);
      return () => clearTimeout(timer);
    }
  }, [detailTab, showDetailModal, selectedCourse]);

  // Handlers
  const showToast = (
    message: string,
    type: 'success' | 'error' = 'success'
  ) => {
    setToast({ show: true, message, type });
    setTimeout(() => {
      setToast((prev) => ({ ...prev, show: false }));
    }, 3000);
  };

  // Course handlers
  const handleCourseClick = (course: Course) => {
    setSelectedCourse(course);
    setDetailTab('overview');
    setShowDetailModal(true);
  };

  const handleEnrollClick = (course: Course) => {
    setSelectedCourse(course);
    setShowEnrollModal(true);
  };

  const handlePurchaseClick = (course: Course) => {
    setSelectedCourse(course);
    setShowPaymentModal(true);
  };

  const handlePaymentSuccess = () => {
    if (selectedCourse) {
      enrollInCourseWithPurchase(selectedCourse);
      
      // Close payment and detail modals
      setShowPaymentModal(false);
      setShowDetailModal(false);
      
      // Show success modal
      setSuccessActionType('purchase');
      setShowSuccessModal(true);
      
      // Keep selectedCourse for success modal, don't reset here
    }
  };

  const handleEnrollment = () => {
    if (!selectedCourse) return;
    
    const targetEnrollmentCode = selectedCourse.enrollmentCode;

    // Check if already enrolled
    if (isEnrolled) {
      setError('You are already enrolled in this course.');
      return;
    }

    if (targetEnrollmentCode === enrollmentCode) {
      // Enroll in course using context
      enrollInCourseWithPurchase(selectedCourse);

      // Close enrollment and detail modals
      setShowEnrollModal(false);
      setShowDetailModal(false);
      
      // Reset form state
      setError('');
      setEnrollmentCode('');
      
      // Show success modal
      setSuccessActionType('enrollment');
      setShowSuccessModal(true);
    } else {
      setError('Invalid enrollment code. Please try again.');
    }
  };


  const handleSuccessModalClose = () => {
    setShowSuccessModal(false);
    setSelectedCourse(null);
  };

  // All available courses
  const availableCourses = mockCourses;

  return (
    <div className='min-h-screen bg-gray-50 p-8'>
      <div className='mx-auto max-w-7xl space-y-6 pb-8'>
        {/* Breadcrumbs - Top Level */}
        <Breadcrumbs />
        
        {/* Header Section */}
        <div className='flex items-center justify-between'>
          <div className='flex items-center space-x-3'>
            <BookOpenIcon className='h-8 w-8 text-[var(--iai-primary)]' />
            <div>
              <h1 className='text-3xl font-bold'>
                Kursus Tersedia
              </h1>
              <p className='text-gray-600'>
                Jelajahi dan daftar kursus profesional
              </p>
            </div>
          </div>
          {SHOW_FEATURE_SETTINGS && (
            <div className='flex gap-2'>
              <Button
                variant='outline'
                onClick={() => setShowFeatureFlagsModal(true)}
                size='sm'
              >
                <SettingsIcon className='h-4 w-4 mr-2' />
                Pengaturan Fitur
              </Button>
            </div>
          )}
        </div>

        {/* Feature Flags Info */}
        {SHOW_FEATURE_SETTINGS && (
          <div className='bg-blue-50 border border-blue-200 rounded-lg p-4'>
            <div className='flex items-center justify-between'>
              <div>
                <h3 className='font-medium text-blue-900'>Pengaturan Saat Ini</h3>
                <div className='flex gap-4 mt-1 text-sm'>
                  <span className={`${flags.enableCoursePurchase ? 'text-green-700' : 'text-gray-500'}`}>
                    Pembelian: {flags.enableCoursePurchase ? 'AKTIF' : 'NONAKTIF'}
                  </span>
                  <span className={`${flags.enableEnrollmentCode ? 'text-green-700' : 'text-gray-500'}`}>
                    Kode Pendaftaran: {flags.enableEnrollmentCode ? 'AKTIF' : 'NONAKTIF'}
                  </span>
                  <span className={`${flags.enableCoursePreview ? 'text-green-700' : 'text-gray-500'}`}>
                    Pratinjau: {flags.enableCoursePreview ? 'AKTIF' : 'NONAKTIF'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}


        {/* Navigation to existing enrollment page */}
        {isEnrolled && (
          <div className='bg-green-50 border border-green-200 rounded-lg p-4'>
            <div className='flex items-center justify-between'>
              <div>
                <h3 className='font-medium text-green-900'>Anda sudah terdaftar!</h3>
                <p className='text-sm text-green-700'>
                  Akses kursus yang Anda daftari dan lanjutkan belajar
                </p>
              </div>
              <Link href='/my-courses'>
                <Button variant="iai">
                  Ke Kursus Saya
                  <ArrowRightIcon className='ml-2 h-4 w-4' />
                </Button>
              </Link>
            </div>
          </div>
        )}

        {/* Courses Grid */}
        <div className='grid gap-6 md:grid-cols-2 lg:grid-cols-3'>
          {availableCourses.map((course) => (
            <CoursePreviewCard
              key={course.id}
              course={course}
              onClick={() => handleCourseClick(course)}
              onEnroll={() => handleEnrollClick(course)}
              onPurchase={() => handlePurchaseClick(course)}
            />
          ))}
        </div>
        
        {availableCourses.length === 0 && (
          <div className='text-center py-12'>
            <BookOpenIcon className='h-12 w-12 text-gray-400 mx-auto mb-4' />
            <h3 className='text-xl font-semibold text-gray-900'>Tidak ada kursus ditemukan</h3>
            <p className='text-gray-600'>Tidak ada kursus yang tersedia saat ini</p>
          </div>
        )}

        {/* Course Detail Modal with ScrollArea for full height scrollable content */}
        <Dialog
          open={showDetailModal}
          onOpenChange={(open) => {
            setShowDetailModal(open);
            if (!open) setSelectedCourse(null);
          }}
        >
          <DialogContent className='sm:max-w-4xl p-0 gap-0 h-[90vh] flex flex-col'>
            {/* Header - Fixed */}
            <div className="p-6 pb-0 flex-shrink-0">
              <DialogHeader>
                <DialogTitle className='text-2xl'>
                  {selectedCourse?.name}
                </DialogTitle>
              </DialogHeader>
            </div>

            {/* Scrollable Content Area */}
            <div 
              ref={scrollContainerRef}
              onScroll={checkScrollable}
              className={`flex-1 overflow-y-auto scrollbar-visible min-h-0 relative scroll-container ${
                showScrollHint ? 'scroll-hint' : ''
              } ${canScrollUp ? 'can-scroll-up' : ''} ${canScrollDown ? 'can-scroll-down' : ''}`}
            >
              <style dangerouslySetInnerHTML={{
                __html: `
                  .scrollbar-visible::-webkit-scrollbar {
                    width: 12px;
                  }
                  .scrollbar-visible::-webkit-scrollbar-track {
                    background: rgb(243 244 246); /* gray-100 */
                    border-radius: 6px;
                  }
                  .scrollbar-visible::-webkit-scrollbar-thumb {
                    background: rgb(209 213 219); /* gray-300 */
                    border-radius: 6px;
                  }
                  .scrollbar-visible::-webkit-scrollbar-thumb:hover {
                    background: rgb(156 163 175); /* gray-400 */
                  }
                  /* For Firefox */
                  .scrollbar-visible {
                    scrollbar-width: thin;
                    scrollbar-color: rgb(209 213 219) rgb(243 244 246);
                  }
                  
                  /* Scroll Indicators */
                  .scroll-container::before {
                    content: "";
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 20px;
                    background: linear-gradient(to bottom, rgba(255,255,255,0.9), transparent);
                    pointer-events: none;
                    z-index: 10;
                    opacity: 0;
                    transition: opacity 0.3s ease;
                  }
                  
                  .scroll-container::after {
                    content: "";
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    height: 20px;
                    background: linear-gradient(to top, rgba(255,255,255,0.9), transparent);
                    pointer-events: none;
                    z-index: 10;
                    opacity: 0;
                    transition: opacity 0.3s ease;
                  }
                  
                  .scroll-container.can-scroll-up::before {
                    opacity: 1;
                  }
                  
                  .scroll-container.can-scroll-down::after {
                    opacity: 1;
                  }
                  
                  /* Pulsing scrollbar animation for initial hint */
                  @keyframes pulse-scrollbar {
                    0%, 100% { opacity: 0.6; }
                    50% { opacity: 1; }
                  }
                  
                  .scroll-hint .scrollbar-visible::-webkit-scrollbar-thumb {
                    animation: pulse-scrollbar 2s infinite;
                  }
                  
                  /* Scroll instruction overlay */
                  .scroll-instruction {
                    position: absolute;
                    top: 50%;
                    right: 20px;
                    transform: translateY(-50%);
                    background: rgba(59, 130, 246, 0.9);
                    color: white;
                    padding: 8px 12px;
                    border-radius: 8px;
                    font-size: 12px;
                    z-index: 20;
                    animation: fadeInOut 4s ease-in-out;
                    pointer-events: none;
                  }
                  
                  @keyframes fadeInOut {
                    0%, 100% { opacity: 0; transform: translateY(-50%) translateX(10px); }
                    10%, 90% { opacity: 1; transform: translateY(-50%) translateX(0); }
                  }
                `
              }} />
              
              <div className="p-6 pt-4">
                {selectedCourse && (
                  <CourseDetailTabs
                    course={selectedCourse}
                    activeTab={detailTab}
                    onTabChange={setDetailTab}
                  />
                )}
              </div>
            </div>




            {/* Scroll Indicator above footer */}
            {canScrollDown && (
              <div className="flex justify-center py-2 bg-gray-50 border-t border-gray-100">
                <div className="flex items-center gap-2 text-gray-500 text-sm animate-bounce">
                  <ArrowDownIcon className="h-4 w-4" />
                  <span>Gulir ke bawah untuk melihat semua detail</span>
                  <ArrowDownIcon className="h-4 w-4" />
                </div>
              </div>
            )}

            {/* Action Buttons - Fixed Footer */}
            {selectedCourse && (
              <div className='p-6 pt-4 border-t bg-white flex-shrink-0'>
                <div className='flex gap-4'>
                  {selectedCourse.isPurchasable && flags.enableCoursePurchase && (
                    <Button
                      onClick={() => handlePurchaseClick(selectedCourse)}
                      variant="iai"
                      className='flex-1'
                    >
                      Beli seharga {selectedCourse.price ? (selectedCourse.currency === 'IDR' ? 'Rp' + new Intl.NumberFormat('id-ID').format(selectedCourse.price) : new Intl.NumberFormat('id-ID', { style: 'currency', currency: selectedCourse.currency || 'IDR' }).format(selectedCourse.price)) : 'Gratis'}
                    </Button>
                  )}
                  {flags.enableEnrollmentCode && selectedCourse.enrollmentCode && (
                    <Button
                      onClick={() => handleEnrollClick(selectedCourse)}
                      variant='outline'
                      className='flex-1'
                    >
                      Gunakan Kode Pendaftaran
                    </Button>
                  )}
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>

        {/* Payment Modal */}
        {selectedCourse && (
          <PaymentModal
            course={selectedCourse}
            isOpen={showPaymentModal}
            onClose={() => setShowPaymentModal(false)}
            onPaymentSuccess={handlePaymentSuccess}
          />
        )}

        {/* Course Success Modal */}
        {selectedCourse && (
          <CourseSuccessModal
            course={selectedCourse}
            isOpen={showSuccessModal}
            onClose={handleSuccessModalClose}
            actionType={successActionType}
          />
        )}

        {/* Enrollment Modal */}
        <Dialog
          open={showEnrollModal}
          onOpenChange={(open) => {
            setShowEnrollModal(open);
            if (!open) {
              setError('');
              setEnrollmentCode('');
              setSelectedCourse(null);
            }
          }}
        >
          <DialogContent className='sm:max-w-md'>
            <DialogHeader>
              <DialogTitle className='text-xl'>
                Daftar di {selectedCourse?.name}
              </DialogTitle>
              <p className='mt-1 text-sm text-gray-600'>
                Masukkan kode pendaftaran yang diberikan oleh instruktur Anda
              </p>
            </DialogHeader>
            <div className='mt-4 space-y-4'>
              <div className='space-y-2 rounded-lg bg-gray-50 p-4'>
                <p className='flex items-center text-gray-700'>
                  <UsersIcon className='mr-2 h-4 w-4' />
                  Instruktur: {selectedCourse?.instructor}
                </p>
                {selectedCourse && (
                  <p className='flex items-center text-gray-700'>
                    <CalendarIcon className='mr-2 h-4 w-4' />
                    Durasi: {new Date(selectedCourse.startDate).toLocaleDateString('id-ID')} - {new Date(selectedCourse.endDate).toLocaleDateString('id-ID')}
                  </p>
                )}
              </div>
              <div className='space-y-1'>
                <div className='relative'>
                  <KeyIcon
                    className={`absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform ${
                      error ? 'text-red-500' : 'text-gray-400'
                    }`}
                  />
                  <Input
                    placeholder={selectedCourse ? `Masukkan kode (contoh: ${selectedCourse.enrollmentCode})` : 'Masukkan kode pendaftaran'}
                    value={enrollmentCode}
                    onChange={(e) => setEnrollmentCode(e.target.value)}
                    className={`pl-10 ${error ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''}`}
                  />
                </div>
                {error && <p className='text-sm text-red-600'>{error}</p>}
              </div>
              <Button onClick={handleEnrollment} className='w-full' size='lg'>
                Selesaikan Pendaftaran
              </Button>
            </div>
          </DialogContent>
        </Dialog>

        {/* Feature Flags Modal */}
        {SHOW_FEATURE_SETTINGS && (
          <Dialog
            open={showFeatureFlagsModal}
            onOpenChange={setShowFeatureFlagsModal}
          >
            <DialogContent className='sm:max-w-md'>
              <DialogHeader>
                <DialogTitle>Pengaturan Fitur</DialogTitle>
              </DialogHeader>
              <div className='space-y-4'>
                {Object.entries(flags).map(([key, value]) => (
                  <div key={key} className='flex items-center justify-between'>
                    <label className='text-sm font-medium'>
                      {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                    </label>
                    <Button
                      variant={value ? 'default' : 'outline'}
                      size='sm'
                      onClick={() => setFlag(key as keyof typeof flags, !value)}
                    >
                      {value ? 'AKTIF' : 'NONAKTIF'}
                    </Button>
                  </div>
                ))}
              </div>
            </DialogContent>
          </Dialog>
        )}


        {/* Toast Notification */}
        {toast.show && (
          <div className='animate-in slide-in-from-bottom-2 fixed right-4 bottom-4 z-50'>
            <div
              className={`flex min-w-[300px] items-center space-x-3 rounded-lg px-6 py-4 shadow-lg ${
                toast.type === 'success'
                  ? 'bg-[var(--iai-primary)] text-white'
                  : toast.type === 'loading'
                  ? 'bg-blue-600 text-white'
                  : 'bg-red-600 text-white'
              } `}
            >
              {toast.type === 'success' ? (
                <CheckCircleIcon className='h-5 w-5 flex-shrink-0' />
              ) : toast.type === 'loading' ? (
                <div className='h-5 w-5 flex-shrink-0 animate-spin border-2 border-white border-t-transparent rounded-full' />
              ) : (
                <XCircleIcon className='h-5 w-5 flex-shrink-0' />
              )}
              <p className='font-medium'>{toast.message}</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AvailableCoursesPage;