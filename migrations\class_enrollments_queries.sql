-- Class Enrollments - Ready-to-use PostgreSQL Queries for NEON Database
-- These queries can be executed directly in NEON SQL Editor

-- ========================================
-- 1. CREATE OPERATIONS
-- ========================================

-- Enroll a student in a class
INSERT INTO class_enrollments (student_id, class_id, status)
VALUES (1, 1, 'active')
RETURNING id, student_id, class_id, enrolled_at, status;

-- Bulk enroll multiple students in a class
INSERT INTO class_enrollments (student_id, class_id, status)
VALUES 
    (1, 1, 'active'),
    (2, 1, 'active'),
    (3, 1, 'active')
RETURNING id, student_id, class_id, enrolled_at, status;

-- ========================================
-- 2. READ OPERATIONS
-- ========================================

-- Get all enrollments with student and class details
SELECT 
    ce.id,
    ce.student_id,
    ce.class_id,
    ce.enrolled_at,
    ce.status,
    ce.created_at,
    ce.updated_at,
    u.name AS student_name,
    u.email AS student_email,
    c.name AS class_name,
    c.description AS class_description
FROM class_enrollments ce
LEFT JOIN users u ON ce.student_id = u.id
LEFT JOIN classes c ON ce.class_id = c.id
ORDER BY ce.enrolled_at DESC;

-- Get all students enrolled in a specific class
SELECT 
    ce.id AS enrollment_id,
    ce.enrolled_at,
    ce.status,
    u.id AS student_id,
    u.name AS student_name,
    u.email AS student_email
FROM class_enrollments ce
JOIN users u ON ce.student_id = u.id
WHERE ce.class_id = 1 -- Replace with actual class_id
  AND ce.status = 'active'
ORDER BY u.name;

-- Get all classes a specific student is enrolled in
SELECT 
    ce.id AS enrollment_id,
    ce.enrolled_at,
    ce.status,
    c.id AS class_id,
    c.name AS class_name,
    c.description AS class_description,
    c.cover_picture
FROM class_enrollments ce
JOIN classes c ON ce.class_id = c.id
WHERE ce.student_id = 1 -- Replace with actual student_id
  AND ce.status = 'active'
ORDER BY ce.enrolled_at DESC;

-- Check if a student is enrolled in a specific class
SELECT 
    ce.id,
    ce.status,
    ce.enrolled_at
FROM class_enrollments ce
WHERE ce.student_id = 1 -- Replace with actual student_id
  AND ce.class_id = 1   -- Replace with actual class_id;

-- Get enrollment statistics for a class
SELECT 
    c.id AS class_id,
    c.name AS class_name,
    COUNT(ce.id) AS total_enrollments,
    COUNT(CASE WHEN ce.status = 'active' THEN 1 END) AS active_enrollments,
    COUNT(CASE WHEN ce.status = 'inactive' THEN 1 END) AS inactive_enrollments
FROM classes c
LEFT JOIN class_enrollments ce ON c.id = ce.class_id
WHERE c.id = 1 -- Replace with actual class_id
GROUP BY c.id, c.name;

-- Get recent enrollments (last 30 days)
SELECT 
    ce.id,
    ce.enrolled_at,
    u.name AS student_name,
    c.name AS class_name,
    ce.status
FROM class_enrollments ce
JOIN users u ON ce.student_id = u.id
JOIN classes c ON ce.class_id = c.id
WHERE ce.enrolled_at >= NOW() - INTERVAL '30 days'
ORDER BY ce.enrolled_at DESC;

-- ========================================
-- 3. UPDATE OPERATIONS
-- ========================================

-- Update enrollment status
UPDATE class_enrollments 
SET status = 'inactive', updated_at = NOW()
WHERE id = 1 -- Replace with actual enrollment_id
RETURNING id, student_id, class_id, status, updated_at;

-- Reactivate enrollment
UPDATE class_enrollments 
SET status = 'active', updated_at = NOW()
WHERE id = 1 -- Replace with actual enrollment_id
RETURNING id, student_id, class_id, status, updated_at;

-- Bulk update status for multiple enrollments
UPDATE class_enrollments 
SET status = 'inactive', updated_at = NOW()
WHERE id IN (1, 2, 3) -- Replace with actual enrollment_ids
RETURNING id, student_id, class_id, status, updated_at;

-- ========================================
-- 4. DELETE OPERATIONS
-- ========================================

-- Remove a specific enrollment
DELETE FROM class_enrollments 
WHERE id = 1 -- Replace with actual enrollment_id
RETURNING id, student_id, class_id;

-- Remove all enrollments for a specific student from a class
DELETE FROM class_enrollments 
WHERE student_id = 1 -- Replace with actual student_id
  AND class_id = 1   -- Replace with actual class_id
RETURNING id, student_id, class_id;

-- Remove all inactive enrollments (cleanup)
DELETE FROM class_enrollments 
WHERE status = 'inactive'
RETURNING id, student_id, class_id;

-- ========================================
-- 5. ADVANCED QUERIES
-- ========================================

-- Get classes with enrollment counts
SELECT 
    c.id,
    c.name,
    c.description,
    COUNT(ce.id) AS total_students,
    COUNT(CASE WHEN ce.status = 'active' THEN 1 END) AS active_students
FROM classes c
LEFT JOIN class_enrollments ce ON c.id = ce.class_id
GROUP BY c.id, c.name, c.description
ORDER BY active_students DESC;

-- Get students not enrolled in any class
SELECT 
    u.id,
    u.name,
    u.email
FROM users u
WHERE u.role = 'student'
  AND u.id NOT IN (
    SELECT DISTINCT student_id 
    FROM class_enrollments 
    WHERE status = 'active'
  )
ORDER BY u.name;

-- Get enrollment history for a student
SELECT 
    ce.id,
    c.name AS class_name,
    ce.enrolled_at,
    ce.status,
    ce.created_at,
    ce.updated_at
FROM class_enrollments ce
JOIN classes c ON ce.class_id = c.id
WHERE ce.student_id = 1 -- Replace with actual student_id
ORDER BY ce.enrolled_at DESC;

-- Find duplicate enrollments (should not exist due to unique constraint)
SELECT 
    student_id,
    class_id,
    COUNT(*) as enrollment_count
FROM class_enrollments
GROUP BY student_id, class_id
HAVING COUNT(*) > 1;

-- ========================================
-- 6. MAINTENANCE QUERIES
-- ========================================

-- Check table structure
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_name = 'class_enrollments'
ORDER BY ordinal_position;

-- Check foreign key constraints
SELECT 
    tc.constraint_name,
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY'
  AND tc.table_name = 'class_enrollments';

-- Check indexes
SELECT 
    indexname,
    indexdef
FROM pg_indexes
WHERE tablename = 'class_enrollments';