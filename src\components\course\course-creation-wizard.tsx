'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { ChevronLeft, ChevronRight, Check } from 'lucide-react';
import { cn } from '@/lib/utils';

// Step components
import { BasicInfoStep } from './steps/basic-info-step';
import { ModuleStructureStep } from './steps/module-structure-step';
import { ContentCreationStep } from './steps/content-creation-step';
import { PublishingStep } from './steps/publishing-step';
import { AdmissionsStep } from './steps/admissions-step';
import { AcademicsStep } from './steps/academics-step';
import { TuitionFinancingStep } from './steps/tuition-financing-step';
import { CareersStep } from './steps/careers-step';
import { StudentExperienceStep } from './steps/student-experience-step';

export interface CourseData {
  // Basic Info
  name: string;
  description: string;
  courseCode: string;
  type: 'self_paced' | 'verified';
  startDate?: Date;
  endDate?: Date;
  coverImage?: File;
  coverImagePreview?: string;
  isPurchasable?: boolean;
  price?: number;
  currency?: string;
  previewMode?: boolean;
  
  // Module Structure
  modules: ModuleData[];
  
  // Publishing
  isPublished: boolean;
  assignedClasses: number[];
  finalExam?: QuizData;

  // Course Details
  admissions?: AdmissionsData;
  academics?: AcademicsData;
  tuitionAndFinancing?: TuitionAndFinancingData;
  careers?: CareersData;
  studentExperience?: StudentExperienceData;
}

export interface ModuleData {
  id: string;
  name: string;
  description: string;
  orderIndex: number;
  chapters: ChapterData[];
  hasModuleQuiz: boolean;
  moduleQuiz?: QuizData;
}

export interface ChapterData {
  id: string;
  name: string;
  content: any[]; // Changed to any[] to accommodate JSON structure
  orderIndex: number;
  hasChapterQuiz: boolean;
  chapterQuiz?: QuizData;
}

export interface QuizData {
  id: string;
  name: string;
  description: string;
  questions: QuestionData[];
  timeLimit?: number;
  minimumScore: number;
}

export interface AdmissionsData {
  requirements: string[];
  applicationDeadline: string;
  prerequisites: string[];
}

export interface AcademicsData {
  credits: number;
  workload: string;
  assessment: string[];
}

export interface TuitionAndFinancingData {
  totalCost: number;
  paymentOptions: string[];
  scholarships: string[];
}

export interface CareersData {
  outcomes: string[];
  industries: string[];
  averageSalary: string;
}

export interface StudentExperienceData {
  testimonials: { name: string; feedback: string }[];
  facilities: string[];
  support: string[];
}

export interface QuestionData {
  id: string;
  type: 'multiple_choice' | 'true_false' | 'essay';
  question: any[]; // Changed to any[] to accommodate JSON structure
  options?: { content: any[]; isCorrect: boolean }[]; // Updated for new JSON structure
  essayAnswer?: string | null; // Renamed from correctAnswer, can be null
  explanation?: any[] | null; // New column, can be null
  points: number;
  orderIndex: number;
}

const STEPS = [
  {
    id: 'basic-info',
    title: 'Informasi Dasar',
    description: 'Detail course dan pengaturan dasar'
  },
  {
    id: 'module-structure',
    title: 'Struktur Modul',
    description: 'Buat modul dan chapter untuk course'
  },
  {
    id: 'content-creation',
    title: 'Pembuatan Konten',
    description: 'Tambahkan konten dan quiz untuk setiap chapter'
  },
  {
    id: 'admissions',
    title: 'Penerimaan',
    description: 'Detail penerimaan dan persyaratan'
  },
  {
    id: 'academics',
    title: 'Akademik',
    description: 'Detail kredit, beban kerja, dan penilaian'
  },
  {
    id: 'tuition-financing',
    title: 'Biaya & Pembiayaan',
    description: 'Informasi biaya dan opsi pembayaran'
  },
  {
    id: 'careers',
    title: 'Karir',
    description: 'Prospek karir dan informasi gaji'
  },
  {
    id: 'student-experience',
    title: 'Pengalaman Siswa',
    description: 'Testimoni, fasilitas, dan dukungan'
  },
  {
    id: 'publishing',
    title: 'Publikasi',
    description: 'Review dan publikasikan course'
  }
];

interface CourseCreationWizardProps {
  onComplete: (courseData: CourseData) => Promise<void>;
  onCancel: () => void;
  initialData?: Partial<CourseData>;
}

export function CourseCreationWizard({ 
  onComplete, 
  onCancel, 
  initialData 
}: CourseCreationWizardProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [courseData, setCourseData] = useState<CourseData>({
    name: initialData?.name || '',
    description: initialData?.description || '',
    courseCode: initialData?.courseCode || '',
    type: initialData?.type || 'self_paced',
    startDate: initialData?.startDate,
    endDate: initialData?.endDate,
    coverImage: initialData?.coverImage,
    coverImagePreview: initialData?.coverImagePreview,
    isPurchasable: initialData?.isPurchasable ?? false,
    price: initialData?.price,
    currency: initialData?.currency || '',
    previewMode: initialData?.previewMode ?? false,
    modules: initialData?.modules || [],
    isPublished: initialData?.isPublished ?? false,
    assignedClasses: initialData?.assignedClasses || [],
    finalExam: initialData?.finalExam,
    admissions: initialData?.admissions || { requirements: [], applicationDeadline: '', prerequisites: [] },
    academics: initialData?.academics || { credits: 0, workload: '', assessment: [] },
    tuitionAndFinancing: initialData?.tuitionAndFinancing || { totalCost: 0, paymentOptions: [], scholarships: [] },
    careers: initialData?.careers || { outcomes: [], industries: [], averageSalary: '' },
    studentExperience: initialData?.studentExperience || { testimonials: [], facilities: [], support: [] },
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Load AI generated data from sessionStorage on component mount
  useEffect(() => {
    const loadAIGeneratedData = () => {
      try {
        const aiGeneratedData = sessionStorage.getItem('ai_generated_course_data');
        if (aiGeneratedData) {
          const parsedData = JSON.parse(aiGeneratedData);
          
          // Merge AI generated data with existing course data
          setCourseData(prev => ({
            ...prev,
            name: parsedData.name || prev.name,
            description: parsedData.description || prev.description,
            courseCode: parsedData.courseCode || prev.courseCode,
            modules: parsedData.modules || prev.modules,
            finalExam: parsedData.finalExam || prev.finalExam,
            ...initialData // initialData takes precedence
          }));
          
          // Clear the session storage after loading
          sessionStorage.removeItem('ai_generated_course_data');
          
          // If we have AI generated modules, skip to content creation step
          if (parsedData.modules && parsedData.modules.length > 0) {
            setCurrentStep(2); // Skip to Content Creation step
          }
        }
      } catch (error) {
        console.error('Error loading AI generated data:', error);
      }
    };

    loadAIGeneratedData();
  }, [initialData]);

  const updateCourseData = (updates: Partial<CourseData>) => {
    setCourseData(prev => ({ ...prev, ...updates }));
  };

  const validateStepData = (step: number): boolean => {
    switch (step) {
      case 0: // Basic Info
        return !!courseData.name && !!courseData.description && !!courseData.courseCode;
      case 1: // Module Structure
        return courseData.modules.length > 0 &&
               courseData.modules.every(module =>
                 !!module.name && module.chapters.length > 0
               );
      case 2: // Content Creation
        return courseData.modules.every(module =>
          module.chapters.every(chapter => !!chapter.content)
        );
      case 3: // Admissions
        return !!courseData.admissions &&
               (courseData.admissions.requirements.length > 0 ||
                !!courseData.admissions.applicationDeadline ||
                courseData.admissions.prerequisites.length > 0);
      case 4: // Academics
        return !!courseData.academics &&
               (courseData.academics.credits > 0 ||
                !!courseData.academics.workload ||
                courseData.academics.assessment.length > 0);
      case 5: // Tuition & Financing
        return !!courseData.tuitionAndFinancing &&
               (!!courseData.tuitionAndFinancing.totalCost ||
                courseData.tuitionAndFinancing.paymentOptions.length > 0 ||
                courseData.tuitionAndFinancing.scholarships.length > 0);
      case 6: // Careers
        return !!courseData.careers &&
               (courseData.careers.outcomes.length > 0 ||
                courseData.careers.industries.length > 0 ||
                !!courseData.careers.averageSalary);
      case 7: // Student Experience
        return !!courseData.studentExperience &&
               (courseData.studentExperience.testimonials.length > 0 ||
                courseData.studentExperience.facilities.length > 0 ||
                courseData.studentExperience.support.length > 0);
      case 8: // Publishing
        return true; // Publishing step doesn't have its own data to validate
      default:
        return false;
    }
  };

  const canProceedToNext = () => {
    return validateStepData(currentStep);
  };

  const handleNext = () => {
    if (currentStep < STEPS.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleComplete = async () => {
    setIsSubmitting(true);
    try {
      await onComplete(courseData);
    } catch (error) {
      console.error('Error creating course:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <BasicInfoStep
            data={courseData}
            onUpdate={updateCourseData}
          />
        );
      case 1:
        return (
          <ModuleStructureStep
            data={courseData}
            onUpdate={updateCourseData}
          />
        );
      case 2:
        return (
          <ContentCreationStep
            data={courseData}
            onUpdate={updateCourseData}
          />
        );
      case 3:
        return (
          <AdmissionsStep
            data={courseData}
            onUpdate={updateCourseData}
          />
        );
      case 4:
        return (
          <AcademicsStep
            data={courseData}
            onUpdate={updateCourseData}
          />
        );
      case 5:
        return (
          <TuitionFinancingStep
            data={courseData}
            onUpdate={updateCourseData}
          />
        );
      case 6:
        return (
          <CareersStep
            data={courseData}
            onUpdate={updateCourseData}
          />
        );
      case 7:
        return (
          <StudentExperienceStep
            data={courseData}
            onUpdate={updateCourseData}
          />
        );
      case 8:
        return (
          <PublishingStep
            data={courseData}
            onPublish={handleComplete}
            isPublishing={isSubmitting}
          />
        );
      default:
        return null;
    }
  };

  const progressPercentage = ((currentStep + 1) / STEPS.length) * 100;

  return (
    <div className="w-full p-6 space-y-6">
      {/* Header */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Buat Course Baru</h1>
            <p className="text-muted-foreground">
              Ikuti langkah-langkah berikut untuk membuat course yang lengkap
            </p>
          </div>
          <Button variant="outline" onClick={onCancel}>
            Batal
          </Button>
        </div>
        
        {/* Progress */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Langkah {currentStep + 1} dari {STEPS.length}</span>
            <span>{Math.round(progressPercentage)}% selesai</span>
          </div>
          <Progress value={progressPercentage} className="h-2" />
        </div>
      </div>

      {/* Steps Navigation */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            {STEPS.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div className="flex flex-col items-center space-y-2">
                  <div className={cn(
                    "w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium",
                    index < currentStep 
                      ? "bg-primary text-primary-foreground"
                      : index === currentStep
                      ? "bg-primary text-primary-foreground"
                      : "bg-muted text-muted-foreground"
                  )}>
                    {index < currentStep ? (
                      <Check className="w-5 h-5" />
                    ) : (
                      index + 1
                    )}
                  </div>
                  <div className="text-center">
                    <div className={cn(
                      "text-sm font-medium",
                      index <= currentStep ? "text-foreground" : "text-muted-foreground"
                    )}>
                      {step.title}
                    </div>
                    <div className="text-xs text-muted-foreground max-w-24">
                      {step.description}
                    </div>
                  </div>
                </div>
                {index < STEPS.length - 1 && (
                  <Separator className="w-16 mx-4" />
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Step Content */}
      <Card>
        <CardHeader>
          <CardTitle>{STEPS[currentStep].title}</CardTitle>
          <CardDescription>{STEPS[currentStep].description}</CardDescription>
        </CardHeader>
        <CardContent>
          {renderStepContent()}
        </CardContent>
      </Card>

      {/* Navigation Buttons */}
      <div className="flex justify-between">
        <Button 
          variant="outline" 
          onClick={handlePrevious}
          disabled={currentStep === 0}
        >
          <ChevronLeft className="w-4 h-4 mr-2" />
          Sebelumnya
        </Button>
        
        <div className="flex space-x-2">
          {currentStep === STEPS.length - 1 ? (
            <Button 
              onClick={handleComplete}
              disabled={!canProceedToNext() || isSubmitting}
            >
              {isSubmitting ? 'Membuat Course...' : 'Selesai & Buat Course'}
            </Button>
          ) : (
            <Button 
              onClick={handleNext}
              disabled={!canProceedToNext()}
            >
              Selanjutnya
              <ChevronRight className="w-4 h-4 ml-2" />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}