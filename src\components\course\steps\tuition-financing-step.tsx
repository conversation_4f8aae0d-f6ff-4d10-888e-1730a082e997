import React from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { CourseData, TuitionAndFinancingData } from '../course-creation-wizard';

interface TuitionFinancingStepProps {
  data: CourseData;
  onUpdate: (updates: Partial<CourseData>) => void;
}

export function TuitionFinancingStep({ data, onUpdate }: TuitionFinancingStepProps) {
  const tuitionAndFinancing = data.tuitionAndFinancing || { totalCost: 0, paymentOptions: [], scholarships: [] };

  const handleUpdate = (field: keyof TuitionAndFinancingData, value: string | number | string[]) => {
    onUpdate({
      tuitionAndFinancing: {
        ...tuitionAndFinancing,
        [field]: value,
      },
    });
  };

  return (
    <div className="space-y-4">
      <div>
        <Label htmlFor="totalCost">Total Biaya</Label>
        <Input
          id="totalCost"
          type="number"
          value={tuitionAndFinancing.totalCost}
          onChange={(e) => handleUpdate('totalCost', parseFloat(e.target.value))}
          placeholder="Contoh: 6000000"
        />
      </div>
      <div>
        <Label htmlFor="paymentOptions">Opsi Pembayaran (pisahkan dengan koma)</Label>
        <Textarea
          id="paymentOptions"
          value={tuitionAndFinancing.paymentOptions.join(', ')}
          onChange={(e) => handleUpdate('paymentOptions', e.target.value.split(',').map(s => s.trim()))}
          placeholder="Contoh: Pembayaran penuh (diskon 5%), Cicilan bulanan (3 bulan)"
        />
      </div>
      <div>
        <Label htmlFor="scholarships">Beasiswa (pisahkan dengan koma)</Label>
        <Textarea
          id="scholarships"
          value={tuitionAndFinancing.scholarships.join(', ')}
          onChange={(e) => handleUpdate('scholarships', e.target.value.split(',').map(s => s.trim()))}
          placeholder="Contoh: Beasiswa berbasis prestasi (hingga 50% off), Diskon early bird (15% off)"
        />
      </div>
    </div>
  );
}