import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Play,
  FileText,
  BookMarked,
  CheckCircle2,
  ChevronUp,
  ChevronDown,
  Timer,
  Download,
  Eye,
  ExternalLink
} from 'lucide-react';
import { ContentItemProps } from '@/types/lms';

export const ContentItem: React.FC<ContentItemProps> = ({
  content,
  onToggleComplete,
  isExpanded,
  onToggleExpand
}) => {
  const handleDownloadMarkdownAsPDF = () => {
    // Create a new window for printing
    const printWindow = window.open('', '_blank');
    if (!printWindow) return;

    // Create HTML content for the markdown
    const htmlContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>${content.title}</title>
          <style>
            @media print {
              @page {
                size: A4;
                margin: 2cm;
              }
              body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
                line-height: 1.6;
                color: #333;
              }
            }
            body {
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
              line-height: 1.6;
              color: #333;
              max-width: 800px;
              margin: 0 auto;
              padding: 20px;
            }
            h1 { color: #2d3748; font-size: 2em; margin-bottom: 1em; }
            h2 { color: #4a5568; font-size: 1.5em; margin: 1.5em 0 0.5em; }
            h3 { color: #4a5568; font-size: 1.25em; margin: 1.2em 0 0.5em; }
            h4 { color: #718096; font-size: 1.1em; margin: 1em 0 0.5em; }
            p { margin-bottom: 1em; }
            ul, ol { margin-bottom: 1em; padding-left: 2em; }
            li { margin-bottom: 0.25em; }
            blockquote {
              border-left: 4px solid #3182ce;
              background: #ebf8ff;
              padding: 1em;
              margin: 1em 0;
              font-style: italic;
            }
            code {
              background: #f7fafc;
              padding: 0.2em 0.4em;
              border-radius: 3px;
              font-family: 'Courier New', monospace;
              font-size: 0.9em;
            }
            pre {
              background: #2d3748;
              color: #f7fafc;
              padding: 1em;
              border-radius: 5px;
              overflow-x: auto;
              margin: 1em 0;
            }
            pre code {
              background: none;
              padding: 0;
              color: inherit;
            }
            table {
              border-collapse: collapse;
              width: 100%;
              margin: 1em 0;
            }
            th, td {
              border: 1px solid #e2e8f0;
              padding: 0.5em;
              text-align: left;
            }
            th {
              background: #f7fafc;
              font-weight: 600;
            }
            hr {
              border: none;
              height: 1px;
              background: #e2e8f0;
              margin: 2em 0;
            }
            strong { font-weight: 600; }
            em { font-style: italic; }
          </style>
        </head>
        <body>
          <h1>${content.title}</h1>
          <div id="markdown-content"></div>
        </body>
      </html>
    `;

    printWindow.document.write(htmlContent);
    printWindow.document.close();

    // Convert markdown to HTML and insert it
    const markdownDiv = printWindow.document.getElementById('markdown-content');
    if (markdownDiv) {
      // Simple markdown to HTML conversion for basic formatting
      let htmlText = '';
      
      // Check if content.content is a string or ContentBlock array
      if (typeof content.content === 'string') {
        htmlText = content.content;
      } else if (Array.isArray(content.content)) {
        // Convert ContentBlock array to string
        htmlText = content.content.map(block => 
          block.type === 'text' ? block.value : ''
        ).join('');
      }
      
      // Headers
      htmlText = htmlText.replace(/^### (.*$)/gim, '<h3>$1</h3>');
      htmlText = htmlText.replace(/^## (.*$)/gim, '<h2>$1</h2>');
      htmlText = htmlText.replace(/^# (.*$)/gim, '<h1>$1</h1>');
      
      // Bold and italic
      htmlText = htmlText.replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>');
      htmlText = htmlText.replace(/\*(.*)\*/gim, '<em>$1</em>');
      
      // Lists
      htmlText = htmlText.replace(/^\* (.*$)/gim, '<li>$1</li>');
      htmlText = htmlText.replace(/(<li>.*<\/li>)/gim, '<ul>$1</ul>');
      htmlText = htmlText.replace(/^\d+\. (.*$)/gim, '<li>$1</li>');
      
      // Line breaks to paragraphs
      htmlText = htmlText.replace(/\n\n/g, '</p><p>');
      htmlText = '<p>' + htmlText + '</p>';
      
      // Clean up empty paragraphs
      htmlText = htmlText.replace(/<p><\/p>/g, '');
      
      markdownDiv.innerHTML = htmlText;
    }

    // Wait for content to load then print
    setTimeout(() => {
      printWindow.focus();
      printWindow.print();
      printWindow.close();
    }, 250);
  };
  const getContentIcon = () => {
    switch (content.type) {
      case 'video':
        return <Play className='h-4 w-4 text-red-500' />;
      case 'pdf':
        return <FileText className='h-4 w-4 text-red-600' />;
      case 'zoom-recording':
        return <Play className='h-4 w-4 text-blue-500' />;
      default:
        return <BookMarked className='h-4 w-4 text-blue-500' />;
    }
  };

  const getContentTypeLabel = () => {
    switch (content.type) {
      case 'video':
        return 'Video';
      case 'pdf':
        return 'PDF Document';
      case 'zoom-recording':
        return 'Zoom Recording';
      default:
        return 'Reading Material';
    }
  };

  return (
    <Card className='my-2 ml-6 border-l-4 border-l-blue-200'>
      <CardContent className='py-3'>
        <div className='flex flex-col'>
          <div
            className='flex cursor-pointer items-center justify-between'
            onClick={onToggleExpand}
          >
            <div className='flex flex-1 items-center space-x-3'>
              {getContentIcon()}
              <div className='flex-1'>
                <span className='text-sm font-medium'>{content.title}</span>
                <div className='mt-1 flex items-center space-x-2'>
                  <Badge variant='outline' className='text-xs'>
                    {getContentTypeLabel()}
                  </Badge>
                  {content.duration && (
                    <Badge variant='outline' className='text-xs'>
                      <Timer className='mr-1 h-3 w-3' />
                      {content.duration} min
                    </Badge>
                  )}
                </div>
              </div>
            </div>
            <div className='flex items-center space-x-2'>
              <Button
                size='sm'
                variant={content.isCompleted ? 'default' : 'outline'}
                className={`min-w-[120px] ${
                  content.isCompleted
                    ? 'bg-green-600 text-white hover:bg-green-700'
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
                onClick={(e) => {
                  e.stopPropagation();
                  onToggleComplete();
                }}
              >
                <CheckCircle2 className='mr-2 h-4 w-4' />
                {content.isCompleted ? 'Completed' : 'Mark Complete'}
              </Button>
              {isExpanded ? (
                <ChevronUp className='h-4 w-4 text-gray-400' />
              ) : (
                <ChevronDown className='h-4 w-4 text-gray-400' />
              )}
            </div>
          </div>

          {isExpanded && (
            <div className='mt-4 border-t pt-4 pl-7'>
              {content.type === 'text' ? (
                <div className='space-y-4'>
                  <div className='prose prose-sm max-w-none text-gray-700'>
                    <ReactMarkdown
                      remarkPlugins={[remarkGfm]}
                      components={{
                        h1: ({ node, ...props }) => (
                          <h1
                            className='mb-4 text-2xl font-bold text-gray-900'
                            {...props}
                          />
                        ),
                        h2: ({ node, ...props }) => (
                          <h2
                            className='mb-3 text-xl font-semibold text-gray-800'
                            {...props}
                          />
                        ),
                        h3: ({ node, ...props }) => (
                          <h3
                            className='mb-2 text-lg font-semibold text-gray-800'
                            {...props}
                          />
                        ),
                        h4: ({ node, ...props }) => (
                          <h4
                            className='mb-2 text-base font-semibold text-gray-700'
                            {...props}
                          />
                        ),
                        p: ({ node, ...props }) => (
                          <p className='mb-3 leading-relaxed' {...props} />
                        ),
                        ul: ({ node, ...props }) => (
                          <ul className='mb-3 ml-4 list-disc' {...props} />
                        ),
                        ol: ({ node, ...props }) => (
                          <ol className='mb-3 ml-4 list-decimal' {...props} />
                        ),
                        li: ({ node, ...props }) => (
                          <li className='mb-1' {...props} />
                        ),
                        blockquote: ({ node, ...props }) => (
                          <blockquote
                            className='mb-4 rounded-r border-l-4 border-blue-200 bg-blue-50 py-2 pl-4 italic'
                            {...props}
                          />
                        ),
                        code: ({ node, className, children, ...props }) => {
                          const match = /language-(\w+)/.exec(className || '');
                          const isInline = !match;
                          return isInline ? (
                            <code
                              className='rounded bg-gray-100 px-1 py-0.5 font-mono text-sm'
                              {...props}
                            >
                              {children}
                            </code>
                          ) : (
                            <code
                              className='block overflow-x-auto rounded bg-gray-900 p-4 font-mono text-sm text-gray-100'
                              {...props}
                            >
                              {children}
                            </code>
                          );
                        },
                        pre: ({ node, ...props }) => (
                          <pre className='mb-4' {...props} />
                        ),
                        table: ({ node, ...props }) => (
                          <div className='mb-4 overflow-x-auto'>
                            <table
                              className='min-w-full rounded border border-gray-200'
                              {...props}
                            />
                          </div>
                        ),
                        thead: ({ node, ...props }) => (
                          <thead className='bg-gray-50' {...props} />
                        ),
                        th: ({ node, ...props }) => (
                          <th
                            className='border border-gray-200 px-3 py-2 text-left font-semibold'
                            {...props}
                          />
                        ),
                        td: ({ node, ...props }) => (
                          <td
                            className='border border-gray-200 px-3 py-2'
                            {...props}
                          />
                        ),
                        hr: ({ node, ...props }) => (
                          <hr className='my-6 border-gray-300' {...props} />
                        ),
                        strong: ({ node, ...props }) => (
                          <strong
                            className='font-semibold text-gray-900'
                            {...props}
                          />
                        ),
                        em: ({ node, ...props }) => (
                          <em className='italic' {...props} />
                        )
                      }}
                    >
                      {typeof content.content === 'string' 
                        ? content.content 
                        : Array.isArray(content.content) 
                          ? content.content.map(block => 
                              block.type === 'text' ? block.value : ''
                            ).join('')
                          : ''
                      }
                    </ReactMarkdown>
                  </div>
                  <Button
                    size='sm'
                    variant='outline'
                    className='border-blue-200 text-blue-600 hover:bg-blue-50'
                    onClick={handleDownloadMarkdownAsPDF}
                  >
                    <Download className='mr-2 h-4 w-4' />
                    Download as PDF
                  </Button>
                </div>
              ) : content.type === 'pdf' ? (
                <div className='space-y-4'>
                  <div className='rounded-lg bg-gray-50 p-4'>
                    <p className='mb-3 text-gray-600'>
                      PDF Document available for download and study
                    </p>
                    <div className='flex space-x-2'>
                      <Button
                        size='sm'
                        variant='outline'
                        className='border-blue-200 text-blue-600 hover:bg-blue-50'
                      >
                        <Eye className='mr-2 h-4 w-4' />
                        View PDF
                      </Button>
                      <Button
                        size='sm'
                        variant='outline'
                        className='text-gray-600 hover:bg-gray-50'
                      >
                        <Download className='mr-2 h-4 w-4' />
                        Download
                      </Button>
                    </div>
                  </div>
                </div>
              ) : (
                <div className='space-y-4'>
                  <div className='flex aspect-video items-center justify-center rounded-lg bg-gray-100'>
                    <div className='text-center'>
                      <Play className='mx-auto mb-2 h-12 w-12 text-gray-400' />
                      <p className='text-sm text-gray-500'>
                        {content.type === 'zoom-recording'
                          ? 'Zoom Recording'
                          : 'Video Content'}
                      </p>
                      {content.duration && (
                        <p className='text-xs text-gray-400'>
                          {content.duration} minutes
                        </p>
                      )}
                    </div>
                  </div>
                  <div className='flex space-x-2'>
                    <Button
                      size='sm'
                      variant='outline'
                      className='border-blue-200 text-blue-600 hover:bg-blue-50'
                    >
                      <Play className='mr-2 h-4 w-4' />
                      Play Video
                    </Button>
                    <Button
                      size='sm'
                      variant='outline'
                      className='text-gray-600 hover:bg-gray-50'
                    >
                      <ExternalLink className='mr-2 h-4 w-4' />
                      Open in New Tab
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};